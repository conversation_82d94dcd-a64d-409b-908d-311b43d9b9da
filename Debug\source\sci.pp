# FIXED

source/sci.obj: ../source/sci.c
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sci.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_sci.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h
source/sci.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h
source/sci.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h

../source/sci.c: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sci.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_sci.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h: 
