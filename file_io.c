/*
 * file_io.c
 *
 *  Created on: Sep 30, 2015
 *      Author: a0324020
 */

#include "file_io.h"
#include "sci_common.h"
#include "stdio.h"
#include "string.h"

/*
**************************************************************************************************************
*  WRITE DATA TO DISK
*
* Description: This function is used by the user to write data to disk
**************************************************************************************************************
*/

void  file_write (int *buff_ptr, unsigned int buffSize, unsigned int SSEL)
{
    FILE *fp;
    int i;
    int *ptr;
    char sel[1];
    char fname[15] = FILENAME_W;

    //strcpy(fname, FILENAME_W);
    sel[0] = SSEL + '0';  //convert integer to char
    strcat(fname, sel);
    strcat(fname, ".TXT");

    ptr = (int*)buff_ptr;

    fp = fopen(fname, "w");  /*open file for writing; doesn't work with a+ for appending, don't know why*/
    fprintf(fp,"Those are the data from FRAY message RAM.\n");

    for (i = 0; i < (buffSize); i += 4)
       	fprintf(fp,"%4x    %8x        %8x        %8x        %8x \n", buffSize*SSEL+i, *(int*)(ptr+i),  *(int*)(ptr+i+1),  *(int*)(ptr+i+2),  *(int*)(ptr+i+3));

    fclose(fp); /* close the file before ending program */

    return;
}

