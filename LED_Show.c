/*
 * LED_Show.c
 *
 */

#include"LED_Show.h"
#include "fray_node_config.h"

#ifdef PMU_CYCLE
#include "sys_pmu.h"
#endif

#ifdef PMU_CYCLE
volatile unsigned long cycles_PMU_start, cycles_PMU_end, cycles_PMU_measure, cycles_PMU_comp, cycles_PMU_code;
volatile float time_PMU_code;
unsigned int seconds_PMU;
#endif //PMU_CYCLE

void gio_LED_Blinky(unsigned num)
{
   	int temp,delay;
// -- Measurement Initialization; want to measure how many cycles are used to receive data from nodes
#ifdef PMU_CYCLE
   	    _pmuInit_();
   	    _pmuEnableCountersGlobal_();
   	    _pmuSetCountEvent_(pmuCOUNTER0, PMU_CYCLE_COUNT); // PMU_INST_ARCH_EXECUTED
#endif //PMU_Cycle
   	// -- Measurement Execution --
#ifdef PMU_CYCLE
   		_pmuResetCounters_();
   		_pmuStartCounters_(pmuCOUNTER0);
   		cycles_PMU_start = _pmuGetEventCount_(pmuCOUNTER0);
#endif //PMU_Cycle

	/** - Delay Parameter */
	delay = 0x200;

	/** - Turn num-th LED ON */
	gioPORTB->DOUT  = ~(0x01 << num);

	/** - Simple Dealy */
	for(temp=0; temp<delay; temp++);

	/** - Turn num-th LED OFF */
	gioPORTB->DOUT = 0xFF;

//	for(temp=0;temp<delay;temp++);
	// PMU measurement shows that it takes 2 seconds to get those data with gio_LED_blinky()
	// and 48ms without gio_LED_blinky(), it is much longer than the 1 cycle time
	// need to consider the FTU
#ifdef PMU_CYCLE
	_pmuStopCounters_(pmuCOUNTER0);
	cycles_PMU_end = _pmuGetEventCount_(pmuCOUNTER0);
	cycles_PMU_measure = cycles_PMU_end - cycles_PMU_start;
	seconds_PMU = cycles_PMU_measure / (180000000);
#endif //PMU_Cycle

}

