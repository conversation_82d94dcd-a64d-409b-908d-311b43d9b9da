/*
 * hw_emac1.h
 */

/* 
* Copyright (C) 2009-2015 Texas Instruments Incorporated - www.ti.com  
* 
* 
*  Redistribution and use in source and binary forms, with or without 
*  modification, are permitted provided that the following conditions 
*  are met:
*
*    Redistributions of source code must retain the above copyright 
*    notice, this list of conditions and the following disclaimer.
*
*    Redistributions in binary form must reproduce the above copyright
*    notice, this list of conditions and the following disclaimer in the 
*    documentation and/or other materials provided with the   
*    distribution.
*
*    Neither the name of Texas Instruments Incorporated nor the names of
*    its contributors may be used to endorse or promote products derived
*    from this software without specific prior written permission.
*
*  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
*  "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
*  LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
*  A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
*  OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
*  SPECIAL, EXEMPLARY, OR CONS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT 
*  LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
*  DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
*  THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
*  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
*  OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/


#ifndef _HW_EMAC_H_
#define _HW_EMAC_H_

/* USER CODE BEGIN (0) */
/* USER CODE END */

#ifdef __cplusplus
extern "C" {
#endif

/* USER CODE BEGIN (1) */
/* USER CODE END */

#define EMAC_BASE               (0xFCF78000U)
#define EMAC_CTRL_BASE          (0xFCF78800U)
#define EMAC_CTRL_RAM_BASE      (0xFC520000U)

#define EMAC_TXREVID		    (0x0U)
#define EMAC_TXCONTROL		    (0x4U)
#define EMAC_TXTEARDOWN		    (0x8U)
#define EMAC_RXREVID		    (0x10U)
#define EMAC_RXCONTROL		    (0x14U)
#define EMAC_RXTEARDOWN		    (0x18U)
#define EMAC_TXINTSTATRAW	    (0x80U)
#define EMAC_TXINTSTATMASKED	(0x84U)
#define EMAC_TXINTMASKSET	    (0x88U)
#define EMAC_TXINTMASKCLEAR	    (0x8CU)
#define EMAC_MACINVECTOR	    (0x90U)
#define EMAC_MACEOIVECTOR	    (0x94U)
#define EMAC_RXINTSTATRAW	    (0xA0U)
#define EMAC_RXINTSTATMASKED	(0xA4U)
#define EMAC_RXINTMASKSET	    (0xA8U)
#define EMAC_RXINTMASKCLEAR	    (0xACU)
#define EMAC_MACINTSTATRAW	    (0xB0U)
#define EMAC_MACINTSTATMASKED	(0xB4U)
#define EMAC_MACINTMASKSET	    (0xB8U)
#define EMAC_MACINTMASKCLEAR	(0xBCU)
#define EMAC_RXMBPENABLE	    (0x100U)
#define EMAC_RXUNICASTSET	    (0x104U)
#define EMAC_RXUNICASTCLEAR   	(0x108U)
#define EMAC_RXMAXLEN		    (0x10CU)
#define EMAC_RXBUFFEROFFSET	    (0x110U)
#define EMAC_RXFILTERLOWTHRESH	(0x114U)
#define EMAC_RXFLOWTHRESH(n)	((uint32)0x120U + (uint32)((n) * 4U))
#define EMAC_RXFREEBUFFER(n)	((uint32)0x140U + (uint32)((n) * 4U))
#define EMAC_MACCONTROL		    (0x160U)
#define EMAC_MACSTATUS		    (0x164U)
#define EMAC_EMCONTROL		    (0x168U)
#define EMAC_FIFOCONTROL	    (0x16CU)
#define EMAC_MACCONFIG		    (0x170U)
#define EMAC_SOFTRESET		    (0x174U)
#define EMAC_MACSRCADDRLO	    (0x1D0U)
#define EMAC_MACSRCADDRHI	    (0x1D4U)
#define EMAC_MACHASH1		    (0x1D8U)
#define EMAC_MACHASH2		    (0x1DCU)
#define EMAC_BOFFTEST		    (0x1E0U)
#define EMAC_TPACETEST		    (0x1E4U)
#define EMAC_RXPAUSE		    (0x1E8U)
#define EMAC_TXPAUSE		    (0x1ECU)
#define EMAC_RXGOODFRAMES	    (0x200U)
#define EMAC_RXBCASTFRAMES	    (0x204U)
#define EMAC_RXMCASTFRAMES	    (0x208U)
#define EMAC_RXPAUSEFRAMES	    (0x20CU)
#define EMAC_RXCRCERRORS	    (0x210U)
#define EMAC_RXALIGNCODEERRORS	(0x214U)
#define EMAC_RXOVERSIZED	    (0x218U)
#define EMAC_RXJABBER		    (0x21CU)
#define EMAC_RXUNDERSIZED	    (0x220U)
#define EMAC_RXFRAGMENTS	    (0x224U)
#define EMAC_RXFILTERED		    (0x228U)
#define EMAC_RXQOSFILTERED	    (0x22CU)
#define EMAC_RXOCTETS		    (0x230U)
#define EMAC_TXGOODFRAMES	    (0x234U)
#define EMAC_TXBCASTFRAMES	    (0x238U)
#define EMAC_TXMCASTFRAMES	    (0x23CU)
#define EMAC_TXPAUSEFRAMES	    (0x240U)
#define EMAC_TXDEFERRED		    (0x244U)
#define EMAC_TXCOLLISION	    (0x248U)
#define EMAC_TXSINGLECOLL	    (0x24CU)
#define EMAC_TXMULTICOLL	    (0x250U)
#define EMAC_TXEXCESSIVECOLL	(0x254U)
#define EMAC_TXLATECOLL		    (0x258U)
#define EMAC_TXUNDERRUN		    (0x25CU)
#define EMAC_TXCARRIERSENSE   	(0x260U)
#define EMAC_TXOCTETS		    (0x264U)
#define EMAC_FRAME64		    (0x268U)
#define EMAC_FRAME65T127	    (0x26CU)
#define EMAC_FRAME128T255	    (0x270U)
#define EMAC_FRAME256T511	    (0x274U)
#define EMAC_FRAME512T1023	    (0x278U)
#define EMAC_FRAME1024TUP	    (0x27CU)
#define EMAC_NETOCTETS		    (0x208U)
#define EMAC_RXSOFOVERRUNS	    (0x284U)
#define EMAC_RXMOFOVERRUNS	    (0x288U)
#define EMAC_RXDMAOVERRUNS	    (0x28CU)
#define EMAC_MACADDRLO          (0x500U)
#define EMAC_MACADDRHI          (0x504U)
#define EMAC_MACINDEX           (0x508U)
#define EMAC_TXHDP(n)		    ((uint32)0x600U + (uint32)((n) * 4U))
#define EMAC_RXHDP(n)		    ((uint32)0x620U + (uint32)((n) * 4U))
#define EMAC_TXCP(n)		    ((uint32)0x640U + (uint32)((n) * 4U))
#define EMAC_RXCP(n)		    ((uint32)0x660U + (uint32)((n) * 4U))

/**************************************************************************\
* Field Definition Macros
\**************************************************************************/

/* TXREVID */

#define EMAC_TXREVID_TXREV      (0xFFFFFFFFU)
#define EMAC_TXREVID_TXREV_SHIFT     (0x00000000U)


/* TXCONTROL */


#define EMAC_TXCONTROL_TXEN     (0x00000001U)
#define EMAC_TXCONTROL_TXEN_SHIFT    (0x00000000U)
#define EMAC_TXCONTROL_TXDIS	(0x00000000U)


/* TXTEARDOWN */

#define EMAC_TXTEARDOWN_TXTDNCH (0x00000007U)
#define EMAC_TXTEARDOWN_TXTDNCH_SHIFT (0x00000000U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA0 (0x00000000U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA1 (0x00000001U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA2 (0x00000002U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA3 (0x00000003U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA4 (0x00000004U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA5 (0x00000005U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA6 (0x00000006U)
#define EMAC_TXTEARDOWN_TXTDNCH_CHA7 (0x00000007U)


/* RXREVID */

#define EMAC_RXREVID_RXREV      (0xFFFFFFFFU)
#define EMAC_RXREVID_RXREV_SHIFT     (0x00000000U)


/* RXCONTROL */


#define EMAC_RXCONTROL_RXEN     (0x00000001U)
#define EMAC_RXCONTROL_RXEN_SHIFT    (0x00000000U)
#define EMAC_RXCONTROL_RXDIS     (0x00000000U)

/* RXTEARDOWN */



#define EMAC_RXTEARDOWN_RXTDNCH (0x00000007U)
#define EMAC_RXTEARDOWN_RXTDNCH_SHIFT (0x00000000U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA0 (0x00000000U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA1 (0x00000001U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA2 (0x00000002U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA3 (0x00000003U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA4 (0x00000004U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA5 (0x00000005U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA6 (0x00000006U)
#define EMAC_RXTEARDOWN_RXTDNCH_CHA7 (0x00000007U)


/* TXINTSTATRAW */


#define EMAC_TXINTSTATRAW_TX7PEND (0x00000080U)
#define EMAC_TXINTSTATRAW_TX7PEND_SHIFT (0x00000007U)

#define EMAC_TXINTSTATRAW_TX6PEND (0x00000040U)
#define EMAC_TXINTSTATRAW_TX6PEND_SHIFT (0x00000006U)

#define EMAC_TXINTSTATRAW_TX5PEND (0x00000020U)
#define EMAC_TXINTSTATRAW_TX5PEND_SHIFT (0x00000005U)

#define EMAC_TXINTSTATRAW_TX4PEND (0x00000010U)
#define EMAC_TXINTSTATRAW_TX4PEND_SHIFT (0x00000004U)

#define EMAC_TXINTSTATRAW_TX3PEND (0x00000008U)
#define EMAC_TXINTSTATRAW_TX3PEND_SHIFT (0x00000003U)

#define EMAC_TXINTSTATRAW_TX2PEND (0x00000004U)
#define EMAC_TXINTSTATRAW_TX2PEND_SHIFT (0x00000002U)

#define EMAC_TXINTSTATRAW_TX1PEND (0x00000002U)
#define EMAC_TXINTSTATRAW_TX1PEND_SHIFT (0x00000001U)

#define EMAC_TXINTSTATRAW_TX0PEND (0x00000001U)
#define EMAC_TXINTSTATRAW_TX0PEND_SHIFT (0x00000000U)


/* TXINTSTATMASKED */


#define EMAC_TXINTSTATMASKED_TX7PEND (0x00000080U)
#define EMAC_TXINTSTATMASKED_TX7PEND_SHIFT (0x00000007U)

#define EMAC_TXINTSTATMASKED_TX6PEND (0x00000040U)
#define EMAC_TXINTSTATMASKED_TX6PEND_SHIFT (0x00000006U)

#define EMAC_TXINTSTATMASKED_TX5PEND (0x00000020U)
#define EMAC_TXINTSTATMASKED_TX5PEND_SHIFT (0x00000005U)

#define EMAC_TXINTSTATMASKED_TX4PEND (0x00000010U)
#define EMAC_TXINTSTATMASKED_TX4PEND_SHIFT (0x00000004U)

#define EMAC_TXINTSTATMASKED_TX3PEND (0x00000008U)
#define EMAC_TXINTSTATMASKED_TX3PEND_SHIFT (0x00000003U)

#define EMAC_TXINTSTATMASKED_TX2PEND (0x00000004U)
#define EMAC_TXINTSTATMASKED_TX2PEND_SHIFT (0x00000002U)

#define EMAC_TXINTSTATMASKED_TX1PEND (0x00000002U)
#define EMAC_TXINTSTATMASKED_TX1PEND_SHIFT (0x00000001U)

#define EMAC_TXINTSTATMASKED_TX0PEND (0x00000001U)
#define EMAC_TXINTSTATMASKED_TX0PEND_SHIFT (0x00000000U)


/* TXINTMASKSET */


#define EMAC_TXINTMASKSET_TX7MASK (0x00000080U)
#define EMAC_TXINTMASKSET_TX7MASK_SHIFT (0x00000007U)

#define EMAC_TXINTMASKSET_TX6MASK (0x00000040U)
#define EMAC_TXINTMASKSET_TX6MASK_SHIFT (0x00000006U)

#define EMAC_TXINTMASKSET_TX5MASK (0x00000020U)
#define EMAC_TXINTMASKSET_TX5MASK_SHIFT (0x00000005U)

#define EMAC_TXINTMASKSET_TX4MASK (0x00000010U)
#define EMAC_TXINTMASKSET_TX4MASK_SHIFT (0x00000004U)

#define EMAC_TXINTMASKSET_TX3MASK (0x00000008U)
#define EMAC_TXINTMASKSET_TX3MASK_SHIFT (0x00000003U)

#define EMAC_TXINTMASKSET_TX2MASK (0x00000004U)
#define EMAC_TXINTMASKSET_TX2MASK_SHIFT (0x00000002U)

#define EMAC_TXINTMASKSET_TX1MASK (0x00000002U)
#define EMAC_TXINTMASKSET_TX1MASK_SHIFT (0x00000001U)

#define EMAC_TXINTMASKSET_TX0MASK (0x00000001U)
#define EMAC_TXINTMASKSET_TX0MASK_SHIFT (0x00000000U)


/* TXINTMASKCLEAR */


#define EMAC_TXINTMASKCLEAR_TX7MASK (0x00000080U)
#define EMAC_TXINTMASKCLEAR_TX7MASK_SHIFT (0x00000007U)

#define EMAC_TXINTMASKCLEAR_TX6MASK (0x00000040U)
#define EMAC_TXINTMASKCLEAR_TX6MASK_SHIFT (0x00000006U)

#define EMAC_TXINTMASKCLEAR_TX5MASK (0x00000020U)
#define EMAC_TXINTMASKCLEAR_TX5MASK_SHIFT (0x00000005U)

#define EMAC_TXINTMASKCLEAR_TX4MASK (0x00000010U)
#define EMAC_TXINTMASKCLEAR_TX4MASK_SHIFT (0x00000004U)

#define EMAC_TXINTMASKCLEAR_TX3MASK (0x00000008U)
#define EMAC_TXINTMASKCLEAR_TX3MASK_SHIFT (0x00000003U)

#define EMAC_TXINTMASKCLEAR_TX2MASK (0x00000004U)
#define EMAC_TXINTMASKCLEAR_TX2MASK_SHIFT (0x00000002U)

#define EMAC_TXINTMASKCLEAR_TX1MASK (0x00000002U)
#define EMAC_TXINTMASKCLEAR_TX1MASK_SHIFT (0x00000001U)

#define EMAC_TXINTMASKCLEAR_TX0MASK (0x00000001U)
#define EMAC_TXINTMASKCLEAR_TX0MASK_SHIFT (0x00000000U)


/* MACINVECTOR */


#define EMAC_MACINVECTOR_STATPEND (0x08000000U)
#define EMAC_MACINVECTOR_STATPEND_SHIFT (0x0000001BU)

#define EMAC_MACINVECTOR_HOSTPEND (0x04000000U)
#define EMAC_MACINVECTOR_HOSTPEND_SHIFT (0x0000001AU)

#define EMAC_MACINVECTOR_LINKINT0 (0x02000000U)
#define EMAC_MACINVECTOR_LINKINT0_SHIFT (0x00000019U)

#define EMAC_MACINVECTOR_USERINT0 (0x01000000U)
#define EMAC_MACINVECTOR_USERINT0_SHIFT (0x00000018U)

#define EMAC_MACINVECTOR_TXPEND (0x00FF0000U)
#define EMAC_MACINVECTOR_TXPEND_SHIFT (0x00000010U)

#define EMAC_MACINVECTOR_RXTHRESHPEND (0x0000FF00U)
#define EMAC_MACINVECTOR_RXTHRESHPEND_SHIFT (0x00000008U)

#define EMAC_MACINVECTOR_RXPEND (0x000000FFU)
#define EMAC_MACINVECTOR_RXPEND_SHIFT (0x00000000U)


/* MACEOIVECTOR */


#define EMAC_MACEOIVECTOR_INTVECT (0x0000001FU)
#define EMAC_MACEOIVECTOR_INTVECT_SHIFT (0x00000000U)
/*----INTVECT Tokens----*/
#define EMAC_MACEOIVECTOR_INTVECT_C0RXTHRESH (0x00000000U)
#define EMAC_MACEOIVECTOR_INTVECT_C0RX (0x00000001U)
#define EMAC_MACEOIVECTOR_INTVECT_C0TX (0x00000002U)
#define EMAC_MACEOIVECTOR_INTVECT_C0MISC (0x00000003U)
#define EMAC_MACEOIVECTOR_INTVECT_C1RXTHRESH (0x00000004U)
#define EMAC_MACEOIVECTOR_INTVECT_C1RX (0x00000005U)
#define EMAC_MACEOIVECTOR_INTVECT_C1TX (0x00000006U)
#define EMAC_MACEOIVECTOR_INTVECT_C1MISC (0x00000007U)


/* RXINTSTATRAW */


#define EMAC_RXINTSTATRAW_RX7THRESHPEND (0x00008000U)
#define EMAC_RXINTSTATRAW_RX7THRESHPEND_SHIFT (0x0000000FU)

#define EMAC_RXINTSTATRAW_RX6THRESHPEND (0x00004000U)
#define EMAC_RXINTSTATRAW_RX6THRESHPEND_SHIFT (0x0000000EU)

#define EMAC_RXINTSTATRAW_RX5THRESHPEND (0x00002000U)
#define EMAC_RXINTSTATRAW_RX5THRESHPEND_SHIFT (0x0000000DU)

#define EMAC_RXINTSTATRAW_RX4THRESHPEND (0x00001000U)
#define EMAC_RXINTSTATRAW_RX4THRESHPEND_SHIFT (0x0000000CU)

#define EMAC_RXINTSTATRAW_RX3THRESHPEND (0x00000800U)
#define EMAC_RXINTSTATRAW_RX3THRESHPEND_SHIFT (0x0000000BU)

#define EMAC_RXINTSTATRAW_RX2THRESHPEND (0x00000400U)
#define EMAC_RXINTSTATRAW_RX2THRESHPEND_SHIFT (0x0000000AU)

#define EMAC_RXINTSTATRAW_RX1THRESHPEND (0x00000200U)
#define EMAC_RXINTSTATRAW_RX1THRESHPEND_SHIFT (0x00000009U)

#define EMAC_RXINTSTATRAW_RX0THRESHPEND (0x00000100U)
#define EMAC_RXINTSTATRAW_RX0THRESHPEND_SHIFT (0x00000008U)

#define EMAC_RXINTSTATRAW_RX7PEND (0x00000080U)
#define EMAC_RXINTSTATRAW_RX7PEND_SHIFT (0x00000007U)

#define EMAC_RXINTSTATRAW_RX6PEND (0x00000040U)
#define EMAC_RXINTSTATRAW_RX6PEND_SHIFT (0x00000006U)

#define EMAC_RXINTSTATRAW_RX5PEND (0x00000020U)
#define EMAC_RXINTSTATRAW_RX5PEND_SHIFT (0x00000005U)

#define EMAC_RXINTSTATRAW_RX4PEND (0x00000010U)
#define EMAC_RXINTSTATRAW_RX4PEND_SHIFT (0x00000004U)

#define EMAC_RXINTSTATRAW_RX3PEND (0x00000008U)
#define EMAC_RXINTSTATRAW_RX3PEND_SHIFT (0x00000003U)

#define EMAC_RXINTSTATRAW_RX2PEND (0x00000004U)
#define EMAC_RXINTSTATRAW_RX2PEND_SHIFT (0x00000002U)

#define EMAC_RXINTSTATRAW_RX1PEND (0x00000002U)
#define EMAC_RXINTSTATRAW_RX1PEND_SHIFT (0x00000001U)

#define EMAC_RXINTSTATRAW_RX0PEND (0x00000001U)
#define EMAC_RXINTSTATRAW_RX0PEND_SHIFT (0x00000000U)


/* RXINTSTATMASKED */


#define EMAC_RXINTSTATMASKED_RX7THRESHPEND (0x00008000U)
#define EMAC_RXINTSTATMASKED_RX7THRESHPEND_SHIFT (0x0000000FU)

#define EMAC_RXINTSTATMASKED_RX6THRESHPEND (0x00004000U)
#define EMAC_RXINTSTATMASKED_RX6THRESHPEND_SHIFT (0x0000000EU)

#define EMAC_RXINTSTATMASKED_RX5THRESHPEND (0x00002000U)
#define EMAC_RXINTSTATMASKED_RX5THRESHPEND_SHIFT (0x0000000DU)

#define EMAC_RXINTSTATMASKED_RX4THRESHPEND (0x00001000U)
#define EMAC_RXINTSTATMASKED_RX4THRESHPEND_SHIFT (0x0000000CU)

#define EMAC_RXINTSTATMASKED_RX3THRESHPEND (0x00000800U)
#define EMAC_RXINTSTATMASKED_RX3THRESHPEND_SHIFT (0x0000000BU)

#define EMAC_RXINTSTATMASKED_RX2THRESHPEND (0x00000400U)
#define EMAC_RXINTSTATMASKED_RX2THRESHPEND_SHIFT (0x0000000AU)

#define EMAC_RXINTSTATMASKED_RX1THRESHPEND (0x00000200U)
#define EMAC_RXINTSTATMASKED_RX1THRESHPEND_SHIFT (0x00000009U)

#define EMAC_RXINTSTATMASKED_RX0THRESHPEND (0x00000100U)
#define EMAC_RXINTSTATMASKED_RX0THRESHPEND_SHIFT (0x00000008U)

#define EMAC_RXINTSTATMASKED_RX7PEND (0x00000080U)
#define EMAC_RXINTSTATMASKED_RX7PEND_SHIFT (0x00000007U)

#define EMAC_RXINTSTATMASKED_RX6PEND (0x00000040U)
#define EMAC_RXINTSTATMASKED_RX6PEND_SHIFT (0x00000006U)

#define EMAC_RXINTSTATMASKED_RX5PEND (0x00000020U)
#define EMAC_RXINTSTATMASKED_RX5PEND_SHIFT (0x00000005U)

#define EMAC_RXINTSTATMASKED_RX4PEND (0x00000010U)
#define EMAC_RXINTSTATMASKED_RX4PEND_SHIFT (0x00000004U)

#define EMAC_RXINTSTATMASKED_RX3PEND (0x00000008U)
#define EMAC_RXINTSTATMASKED_RX3PEND_SHIFT (0x00000003U)

#define EMAC_RXINTSTATMASKED_RX2PEND (0x00000004U)
#define EMAC_RXINTSTATMASKED_RX2PEND_SHIFT (0x00000002U)

#define EMAC_RXINTSTATMASKED_RX1PEND (0x00000002U)
#define EMAC_RXINTSTATMASKED_RX1PEND_SHIFT (0x00000001U)

#define EMAC_RXINTSTATMASKED_RX0PEND (0x00000001U)
#define EMAC_RXINTSTATMASKED_RX0PEND_SHIFT (0x00000000U)


/* RXINTMASKSET */


#define EMAC_RXINTMASKSET_RX7THRESHMASK (0x00008000U)
#define EMAC_RXINTMASKSET_RX7THRESHMASK_SHIFT (0x0000000FU)

#define EMAC_RXINTMASKSET_RX6THRESHMASK (0x00004000U)
#define EMAC_RXINTMASKSET_RX6THRESHMASK_SHIFT (0x0000000EU)

#define EMAC_RXINTMASKSET_RX5THRESHMASK (0x00002000U)
#define EMAC_RXINTMASKSET_RX5THRESHMASK_SHIFT (0x0000000DU)

#define EMAC_RXINTMASKSET_RX4THRESHMASK (0x00001000U)
#define EMAC_RXINTMASKSET_RX4THRESHMASK_SHIFT (0x0000000CU)

#define EMAC_RXINTMASKSET_RX3THRESHMASK (0x00000800U)
#define EMAC_RXINTMASKSET_RX3THRESHMASK_SHIFT (0x0000000BU)

#define EMAC_RXINTMASKSET_RX2THRESHMASK (0x00000400U)
#define EMAC_RXINTMASKSET_RX2THRESHMASK_SHIFT (0x0000000AU)

#define EMAC_RXINTMASKSET_RX1THRESHMASK (0x00000200U)
#define EMAC_RXINTMASKSET_RX1THRESHMASK_SHIFT (0x00000009U)

#define EMAC_RXINTMASKSET_RX0THRESHMASK (0x00000100U)
#define EMAC_RXINTMASKSET_RX0THRESHMASK_SHIFT (0x00000008U)

#define EMAC_RXINTMASKSET_RX7MASK (0x00000080U)
#define EMAC_RXINTMASKSET_RX7MASK_SHIFT (0x00000007U)

#define EMAC_RXINTMASKSET_RX6MASK (0x00000040U)
#define EMAC_RXINTMASKSET_RX6MASK_SHIFT (0x00000006U)

#define EMAC_RXINTMASKSET_RX5MASK (0x00000020U)
#define EMAC_RXINTMASKSET_RX5MASK_SHIFT (0x00000005U)

#define EMAC_RXINTMASKSET_RX4MASK (0x00000010U)
#define EMAC_RXINTMASKSET_RX4MASK_SHIFT (0x00000004U)

#define EMAC_RXINTMASKSET_RX3MASK (0x00000008U)
#define EMAC_RXINTMASKSET_RX3MASK_SHIFT (0x00000003U)

#define EMAC_RXINTMASKSET_RX2MASK (0x00000004U)
#define EMAC_RXINTMASKSET_RX2MASK_SHIFT (0x00000002U)

#define EMAC_RXINTMASKSET_RX1MASK (0x00000002U)
#define EMAC_RXINTMASKSET_RX1MASK_SHIFT (0x00000001U)

#define EMAC_RXINTMASKSET_RX0MASK (0x00000001U)
#define EMAC_RXINTMASKSET_RX0MASK_SHIFT (0x00000000U)


/* RXINTMASKCLEAR */


#define EMAC_RXINTMASKCLEAR_RX7THRESHMASK (0x00008000U)
#define EMAC_RXINTMASKCLEAR_RX7THRESHMASK_SHIFT (0x0000000FU)

#define EMAC_RXINTMASKCLEAR_RX6THRESHMASK (0x00004000U)
#define EMAC_RXINTMASKCLEAR_RX6THRESHMASK_SHIFT (0x0000000EU)

#define EMAC_RXINTMASKCLEAR_RX5THRESHMASK (0x00002000U)
#define EMAC_RXINTMASKCLEAR_RX5THRESHMASK_SHIFT (0x0000000DU)

#define EMAC_RXINTMASKCLEAR_RX4THRESHMASK (0x00001000U)
#define EMAC_RXINTMASKCLEAR_RX4THRESHMASK_SHIFT (0x0000000CU)

#define EMAC_RXINTMASKCLEAR_RX3THRESHMASK (0x00000800U)
#define EMAC_RXINTMASKCLEAR_RX3THRESHMASK_SHIFT (0x0000000BU)

#define EMAC_RXINTMASKCLEAR_RX2THRESHMASK (0x00000400U)
#define EMAC_RXINTMASKCLEAR_RX2THRESHMASK_SHIFT (0x0000000AU)

#define EMAC_RXINTMASKCLEAR_RX1THRESHMASK (0x00000200U)
#define EMAC_RXINTMASKCLEAR_RX1THRESHMASK_SHIFT (0x00000009U)

#define EMAC_RXINTMASKCLEAR_RX0THRESHMASK (0x00000100U)
#define EMAC_RXINTMASKCLEAR_RX0THRESHMASK_SHIFT (0x00000008U)

#define EMAC_RXINTMASKCLEAR_RX7MASK (0x00000080U)
#define EMAC_RXINTMASKCLEAR_RX7MASK_SHIFT (0x00000007U)

#define EMAC_RXINTMASKCLEAR_RX6MASK (0x00000040U)
#define EMAC_RXINTMASKCLEAR_RX6MASK_SHIFT (0x00000006U)

#define EMAC_RXINTMASKCLEAR_RX5MASK (0x00000020U)
#define EMAC_RXINTMASKCLEAR_RX5MASK_SHIFT (0x00000005U)

#define EMAC_RXINTMASKCLEAR_RX4MASK (0x00000010U)
#define EMAC_RXINTMASKCLEAR_RX4MASK_SHIFT (0x00000004U)

#define EMAC_RXINTMASKCLEAR_RX3MASK (0x00000008U)
#define EMAC_RXINTMASKCLEAR_RX3MASK_SHIFT (0x00000003U)

#define EMAC_RXINTMASKCLEAR_RX2MASK (0x00000004U)
#define EMAC_RXINTMASKCLEAR_RX2MASK_SHIFT (0x00000002U)

#define EMAC_RXINTMASKCLEAR_RX1MASK (0x00000002U)
#define EMAC_RXINTMASKCLEAR_RX1MASK_SHIFT (0x00000001U)

#define EMAC_RXINTMASKCLEAR_RX0MASK (0x00000001U)
#define EMAC_RXINTMASKCLEAR_RX0MASK_SHIFT (0x00000000U)


/* MACINTSTATRAW */


#define EMAC_MACINTSTATRAW_HOSTPEND (0x00000002U)
#define EMAC_MACINTSTATRAW_HOSTPEND_SHIFT (0x00000001U)

#define EMAC_MACINTSTATRAW_STATPEND (0x00000001U)
#define EMAC_MACINTSTATRAW_STATPEND_SHIFT (0x00000000U)


/* MACINTSTATMASKED */


#define EMAC_MACINTSTATMASKED_HOSTPEND (0x00000002U)
#define EMAC_MACINTSTATMASKED_HOSTPEND_SHIFT (0x00000001U)

#define EMAC_MACINTSTATMASKED_STATPEND (0x00000001U)
#define EMAC_MACINTSTATMASKED_STATPEND_SHIFT (0x00000000U)


/* MACINTMASKSET */


#define EMAC_MACINTMASKSET_HOSTMASK (0x00000002U)
#define EMAC_MACINTMASKSET_HOSTMASK_SHIFT (0x00000001U)

#define EMAC_MACINTMASKSET_STATMASK (0x00000001U)
#define EMAC_MACINTMASKSET_STATMASK_SHIFT (0x00000000U)


/* MACINTMASKCLEAR */


#define EMAC_MACINTMASKCLEAR_HOSTMASK (0x00000002U)
#define EMAC_MACINTMASKCLEAR_HOSTMASK_SHIFT (0x00000001U)

#define EMAC_MACINTMASKCLEAR_STATMASK (0x00000001U)
#define EMAC_MACINTMASKCLEAR_STATMASK_SHIFT (0x00000000U)


/* RXMBPENABLE */


#define EMAC_RXMBPENABLE_RXPASSCRC (0x40000000U)
#define EMAC_RXMBPENABLE_RXPASSCRC_SHIFT (0x0000001EU)
#define EMAC_RXMBPENABLE_RXQOSEN (0x20000000U)
#define EMAC_RXMBPENABLE_RXQOSEN_SHIFT (0x0000001DU)
#define EMAC_RXMBPENABLE_RXNOCHAIN (0x10000000U)
#define EMAC_RXMBPENABLE_RXNOCHAIN_SHIFT (0x0000001CU)
#define EMAC_RXMBPENABLE_RXCMFEN (0x01000000U)
#define EMAC_RXMBPENABLE_RXCMFEN_SHIFT (0x00000018U)
#define EMAC_RXMBPENABLE_RXCSFEN (0x00800000U)
#define EMAC_RXMBPENABLE_RXCSFEN_SHIFT (0x00000017U)
#define EMAC_RXMBPENABLE_RXCEFEN (0x00400000U)
#define EMAC_RXMBPENABLE_RXCEFEN_SHIFT (0x00000016U)
#define EMAC_RXMBPENABLE_RXCAFEN (0x00200000U)
#define EMAC_RXMBPENABLE_RXCAFEN_SHIFT (0x00000015U)
/*----RXCAFEN Tokens----*/
#define EMAC_RXMBPENABLE_RXPROMCH (0x00070000U)
#define EMAC_RXMBPENABLE_RXPROMCH_SHIFT (0x00000010U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA0 (0x00000000U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA1 (0x00000001U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA2 (0x00000002U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA3 (0x00000003U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA4 (0x00000004U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA5 (0x00000005U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA6 (0x00000006U)
#define EMAC_RXMBPENABLE_RXPROMCH_CHA7 (0x00000007U)


#define EMAC_RXMBPENABLE_RXBROADEN (0x00002000U)
#define EMAC_RXMBPENABLE_RXBROADEN_SHIFT (0x0000000DU)
#define EMAC_RXMBPENABLE_RXBROADCH (0x00000700U)
#define EMAC_RXMBPENABLE_RXBROADCH_SHIFT (0x00000008U)
/*----RXBROADCH Tokens----*/
#define EMAC_RXMBPENABLE_RXBROADCH_CHA0 (0x00000000U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA1 (0x00000001U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA2 (0x00000002U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA3 (0x00000003U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA4 (0x00000004U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA5 (0x00000005U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA6 (0x00000006U)
#define EMAC_RXMBPENABLE_RXBROADCH_CHA7 (0x00000007U)


#define EMAC_RXMBPENABLE_RXMULTEN (0x00000020U)
#define EMAC_RXMBPENABLE_RXMULTEN_SHIFT (0x00000005U)
#define EMAC_RXMBPENABLE_RXMULTCH (0x00000007U)
#define EMAC_RXMBPENABLE_RXMULTCH_SHIFT (0x00000000U)
/*----RXMULTCH Tokens----*/
#define EMAC_RXMBPENABLE_RXMULTCH_CHA0 (0x00000000U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA1 (0x00000001U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA2 (0x00000002U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA3 (0x00000003U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA4 (0x00000004U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA5 (0x00000005U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA6 (0x00000006U)
#define EMAC_RXMBPENABLE_RXMULTCH_CHA7 (0x00000007U)


/* RXUNICASTSET */


#define EMAC_RXUNICASTSET_RXCH7EN (0x00000080U)
#define EMAC_RXUNICASTSET_RXCH7EN_SHIFT (0x00000007U)
#define EMAC_RXUNICASTSET_RXCH6EN (0x00000040U)
#define EMAC_RXUNICASTSET_RXCH6EN_SHIFT (0x00000006U)
#define EMAC_RXUNICASTSET_RXCH5EN (0x00000020U)
#define EMAC_RXUNICASTSET_RXCH5EN_SHIFT (0x00000005U)
#define EMAC_RXUNICASTSET_RXCH4EN (0x00000010U)
#define EMAC_RXUNICASTSET_RXCH4EN_SHIFT (0x00000004U)
#define EMAC_RXUNICASTSET_RXCH3EN (0x00000008U)
#define EMAC_RXUNICASTSET_RXCH3EN_SHIFT (0x00000003U)
#define EMAC_RXUNICASTSET_RXCH2EN (0x00000004U)
#define EMAC_RXUNICASTSET_RXCH2EN_SHIFT (0x00000002U)
#define EMAC_RXUNICASTSET_RXCH1EN (0x00000002U)
#define EMAC_RXUNICASTSET_RXCH1EN_SHIFT (0x00000001U)
#define EMAC_RXUNICASTSET_RXCH0EN (0x00000001U)
#define EMAC_RXUNICASTSET_RXCH0EN_SHIFT (0x00000000U)

/* RXUNICASTCLEAR */


#define EMAC_RXUNICASTCLEAR_RXCH7EN (0x00000080U)
#define EMAC_RXUNICASTCLEAR_RXCH7EN_SHIFT (0x00000007U)
#define EMAC_RXUNICASTCLEAR_RXCH6EN (0x00000040U)
#define EMAC_RXUNICASTCLEAR_RXCH6EN_SHIFT (0x00000006U)
#define EMAC_RXUNICASTCLEAR_RXCH5EN (0x00000020U)
#define EMAC_RXUNICASTCLEAR_RXCH5EN_SHIFT (0x00000005U)
#define EMAC_RXUNICASTCLEAR_RXCH4EN (0x00000010U)
#define EMAC_RXUNICASTCLEAR_RXCH4EN_SHIFT (0x00000004U)
#define EMAC_RXUNICASTCLEAR_RXCH3EN (0x00000008U)
#define EMAC_RXUNICASTCLEAR_RXCH3EN_SHIFT (0x00000003U)
#define EMAC_RXUNICASTCLEAR_RXCH2EN (0x00000004U)
#define EMAC_RXUNICASTCLEAR_RXCH2EN_SHIFT (0x00000002U)
#define EMAC_RXUNICASTCLEAR_RXCH1EN (0x00000002U)
#define EMAC_RXUNICASTCLEAR_RXCH1EN_SHIFT (0x00000001U)
#define EMAC_RXUNICASTCLEAR_RXCH0EN (0x00000001U)
#define EMAC_RXUNICASTCLEAR_RXCH0EN_SHIFT (0x00000000U)

/* RXMAXLEN */


#define EMAC_RXMAXLEN_RXMAXLEN  (0x0000FFFFU)
#define EMAC_RXMAXLEN_RXMAXLEN_SHIFT (0x00000000U)


/* RXBUFFEROFFSET */


#define EMAC_RXBUFFEROFFSET_RXBUFFEROFFSET (0x0000FFFFU)
#define EMAC_RXBUFFEROFFSET_RXBUFFEROFFSET_SHIFT (0x00000000U)


/* RXFILTERLOWTHRESH */


#define EMAC_RXFILTERLOWTHRESH_RXFILTERTHRESH (0x000000FFU)
#define EMAC_RXFILTERLOWTHRESH_RXFILTERTHRESH_SHIFT (0x00000000U)


/* RX0FLOWTHRESH */


#define EMAC_RX0FLOWTHRESH_RX0FLOWTHRESH (0x000000FFU)
#define EMAC_RX0FLOWTHRESH_RX0FLOWTHRESH_SHIFT (0x00000000U)


/* RX1FLOWTHRESH */


#define EMAC_RX1FLOWTHRESH_RX1FLOWTHRESH (0x000000FFU)
#define EMAC_RX1FLOWTHRESH_RX1FLOWTHRESH_SHIFT (0x00000000U)


/* RX2FLOWTHRESH */


#define EMAC_RX2FLOWTHRESH_RX2FLOWTHRESH (0x000000FFU)
#define EMAC_RX2FLOWTHRESH_RX2FLOWTHRESH_SHIFT (0x00000000U)


/* RX3FLOWTHRESH */


#define EMAC_RX3FLOWTHRESH_RX3FLOWTHRESH (0x000000FFU)
#define EMAC_RX3FLOWTHRESH_RX3FLOWTHRESH_SHIFT (0x00000000U)


/* RX4FLOWTHRESH */


#define EMAC_RX4FLOWTHRESH_RX4FLOWTHRESH (0x000000FFU)
#define EMAC_RX4FLOWTHRESH_RX4FLOWTHRESH_SHIFT (0x00000000U)


/* RX5FLOWTHRESH */


#define EMAC_RX5FLOWTHRESH_RX5FLOWTHRESH (0x000000FFU)
#define EMAC_RX5FLOWTHRESH_RX5FLOWTHRESH_SHIFT (0x00000000U)


/* RX6FLOWTHRESH */


#define EMAC_RX6FLOWTHRESH_RX6FLOWTHRESH (0x000000FFU)
#define EMAC_RX6FLOWTHRESH_RX6FLOWTHRESH_SHIFT (0x00000000U)


/* RX7FLOWTHRESH */


#define EMAC_RX7FLOWTHRESH_RX7FLOWTHRESH (0x000000FFU)
#define EMAC_RX7FLOWTHRESH_RX7FLOWTHRESH_SHIFT (0x00000000U)


/* RX0FREEBUFFER */


#define EMAC_RX0FREEBUFFER_RX0FREEBUF (0x0000FFFFU)
#define EMAC_RX0FREEBUFFER_RX0FREEBUF_SHIFT (0x00000000U)


/* RX1FREEBUFFER */


#define EMAC_RX1FREEBUFFER_RX1FREEBUF (0x0000FFFFU)
#define EMAC_RX1FREEBUFFER_RX1FREEBUF_SHIFT (0x00000000U)


/* RX2FREEBUFFER */


#define EMAC_RX2FREEBUFFER_RX2FREEBUF (0x0000FFFFU)
#define EMAC_RX2FREEBUFFER_RX2FREEBUF_SHIFT (0x00000000U)


/* RX3FREEBUFFER */


#define EMAC_RX3FREEBUFFER_RX3FREEBUF (0x0000FFFFU)
#define EMAC_RX3FREEBUFFER_RX3FREEBUF_SHIFT (0x00000000U)


/* RX4FREEBUFFER */


#define EMAC_RX4FREEBUFFER_RX4FREEBUF (0x0000FFFFU)
#define EMAC_RX4FREEBUFFER_RX4FREEBUF_SHIFT (0x00000000U)


/* RX5FREEBUFFER */


#define EMAC_RX5FREEBUFFER_RX5FREEBUF (0x0000FFFFU)
#define EMAC_RX5FREEBUFFER_RX5FREEBUF_SHIFT (0x00000000U)


/* RX6FREEBUFFER */


#define EMAC_RX6FREEBUFFER_RX6FREEBUF (0x0000FFFFU)
#define EMAC_RX6FREEBUFFER_RX6FREEBUF_SHIFT (0x00000000U)


/* RX7FREEBUFFER */


#define EMAC_RX7FREEBUFFER_RX7FREEBUF (0x0000FFFFU)
#define EMAC_RX7FREEBUFFER_RX7FREEBUF_SHIFT (0x00000000U)


/* MACCONTROL */





#define EMAC_MACCONTROL_RMIISPEED (0x00008000U)
#define EMAC_MACCONTROL_RMIISPEED_SHIFT (0x0000000FU)
#define EMAC_MACCONTROL_RXOFFLENBLOCK (0x00004000U)
#define EMAC_MACCONTROL_RXOFFLENBLOCK_SHIFT (0x0000000EU)
#define EMAC_MACCONTROL_RXOWNERSHIP (0x00002000U)
#define EMAC_MACCONTROL_RXOWNERSHIP_SHIFT (0x0000000DU)
#define EMAC_MACCONTROL_CMDIDLE (0x00000800U)
#define EMAC_MACCONTROL_CMDIDLE_SHIFT (0x0000000BU)
#define EMAC_MACCONTROL_TXSHORTGAPEN (0x00000400U)
#define EMAC_MACCONTROL_TXSHORTGAPEN_SHIFT (0x0000000AU)
#define EMAC_MACCONTROL_TXPTYPE (0x00000200U)
#define EMAC_MACCONTROL_TXPTYPE_SHIFT (0x00000009U)
#define EMAC_MACCONTROL_TXPACE  (0x00000040U)
#define EMAC_MACCONTROL_TXPACE_SHIFT (0x00000006U)
#define EMAC_MACCONTROL_GMIIEN  (0x00000020U)
#define EMAC_MACCONTROL_GMIIEN_SHIFT (0x00000005U)
#define EMAC_MACCONTROL_TXFLOWEN (0x00000010U)
#define EMAC_MACCONTROL_TXFLOWEN_SHIFT (0x00000004U)
#define EMAC_MACCONTROL_RXBUFFERFLOWEN (0x00000008U)
#define EMAC_MACCONTROL_RXBUFFERFLOWEN_SHIFT (0x00000003U)
#define EMAC_MACCONTROL_LOOPBACK (0x00000002U)
#define EMAC_MACCONTROL_LOOPBACK_SHIFT (0x00000001U)
#define EMAC_MACCONTROL_FULLDUPLEX (0x00000001U)
#define EMAC_MACCONTROL_FULLDUPLEX_SHIFT (0x00000000U)


/* MACSTATUS */

#define EMAC_MACSTATUS_IDLE     (0x80000000U)
#define EMAC_MACSTATUS_IDLE_SHIFT    (0x0000001FU)
#define EMAC_MACSTATUS_TXERRCODE (0x00F00000U)
#define EMAC_MACSTATUS_TXERRCODE_SHIFT (0x00000014U)
/*----TXERRCODE Tokens----*/
#define EMAC_MACSTATUS_TXERRCODE_NOERROR (0x00000000U)
#define EMAC_MACSTATUS_TXERRCODE_SOPERROR (0x00000001U)
#define EMAC_MACSTATUS_TXERRCODE_OWNERSHIP (0x00000002U)
#define EMAC_MACSTATUS_TXERRCODE_NOEOP (0x00000003U)
#define EMAC_MACSTATUS_TXERRCODE_NULLPTR (0x00000004U)
#define EMAC_MACSTATUS_TXERRCODE_NULLEN (0x00000005U)
#define EMAC_MACSTATUS_TXERRCODE_LENERROR (0x00000006U)


#define EMAC_MACSTATUS_TXERRCH  (0x00070000U)
#define EMAC_MACSTATUS_TXERRCH_SHIFT (0x00000010U)
/*----TXERRCH Tokens----*/
#define EMAC_MACSTATUS_TXERRCH_CHA0  (0x00000000U)
#define EMAC_MACSTATUS_TXERRCH_CHA1  (0x00000001U)
#define EMAC_MACSTATUS_TXERRCH_CHA2  (0x00000002U)
#define EMAC_MACSTATUS_TXERRCH_CHA3  (0x00000003U)
#define EMAC_MACSTATUS_TXERRCH_CHA4  (0x00000004U)
#define EMAC_MACSTATUS_TXERRCH_CHA5  (0x00000005U)
#define EMAC_MACSTATUS_TXERRCH_CHA6  (0x00000006U)
#define EMAC_MACSTATUS_TXERRCH_CHA7  (0x00000007U)

#define EMAC_MACSTATUS_RXERRCODE (0x0000F000U)
#define EMAC_MACSTATUS_RXERRCODE_SHIFT (0x0000000CU)
/*----RXERRCODE Tokens----*/
#define EMAC_MACSTATUS_RXERRCODE_NOERROR (0x00000000U)
#define EMAC_MACSTATUS_RXERRCODE_OWNERSHIP (0x00000002U)
#define EMAC_MACSTATUS_RXERRCODE_NULLPTR (0x00000004U)


#define EMAC_MACSTATUS_RXERRCH  (0x00000700U)
#define EMAC_MACSTATUS_RXERRCH_SHIFT (0x00000008U)
/*----RXERRCH Tokens----*/
#define EMAC_MACSTATUS_RXERRCH_CHA0  (0x00000000U)
#define EMAC_MACSTATUS_RXERRCH_CHA1  (0x00000001U)
#define EMAC_MACSTATUS_RXERRCH_CHA2  (0x00000002U)
#define EMAC_MACSTATUS_RXERRCH_CHA3  (0x00000003U)
#define EMAC_MACSTATUS_RXERRCH_CHA4  (0x00000004U)
#define EMAC_MACSTATUS_RXERRCH_CHA5  (0x00000005U)
#define EMAC_MACSTATUS_RXERRCH_CHA6  (0x00000006U)
#define EMAC_MACSTATUS_RXERRCH_CHA7  (0x00000007U)




#define EMAC_MACSTATUS_RXQOSACT (0x00000004U)
#define EMAC_MACSTATUS_RXQOSACT_SHIFT (0x00000002U)
#define EMAC_MACSTATUS_RXFLOWACT (0x00000002U)
#define EMAC_MACSTATUS_RXFLOWACT_SHIFT (0x00000001U)
#define EMAC_MACSTATUS_TXFLOWACT (0x00000001U)
#define EMAC_MACSTATUS_TXFLOWACT_SHIFT (0x00000000U)

/* EMCONTROL */


#define EMAC_EMCONTROL_SOFT     (0x00000002U)
#define EMAC_EMCONTROL_SOFT_SHIFT    (0x00000001U)

#define EMAC_EMCONTROL_FREE     (0x00000001U)
#define EMAC_EMCONTROL_FREE_SHIFT    (0x00000000U)


/* FIFOCONTROL */


#define EMAC_FIFOCONTROL_TXCELLTHRESH (0x00000003U)
#define EMAC_FIFOCONTROL_TXCELLTHRESH_SHIFT (0x00000000U)


/* MACCONFIG */

#define EMAC_MACCONFIG_TXCELLDEPTH (0xFF000000U)
#define EMAC_MACCONFIG_TXCELLDEPTH_SHIFT (0x00000018U)

#define EMAC_MACCONFIG_RXCELLDEPTH (0x00FF0000U)
#define EMAC_MACCONFIG_RXCELLDEPTH_SHIFT (0x00000010U)

#define EMAC_MACCONFIG_ADDRESSTYPE (0x0000FF00U)
#define EMAC_MACCONFIG_ADDRESSTYPE_SHIFT (0x00000008U)

#define EMAC_MACCONFIG_MACCFIG  (0x000000FFU)
#define EMAC_MACCONFIG_MACCFIG_SHIFT (0x00000000U)


/* SOFTRESET */


#define EMAC_SOFTRESET_SOFTRESET (0x00000001U)
#define EMAC_SOFTRESET_SOFTRESET_SHIFT (0x00000000U)

/* MACSRCADDRLO */


#define EMAC_MACSRCADDRLO_MACSRCADDR0 (0x0000FF00U)
#define EMAC_MACSRCADDRLO_MACSRCADDR0_SHIFT (0x00000008U)
#define EMAC_MACSRCADDRLO_MACSRCADDR1 (0x000000FFU)
#define EMAC_MACSRCADDRLO_MACSRCADDR1_SHIFT (0x00000000U)


/* MACSRCADDRHI */

#define EMAC_MACSRCADDRHI_MACSRCADDR2 (0xFF000000U)
#define EMAC_MACSRCADDRHI_MACSRCADDR2_SHIFT (0x00000018U)

#define EMAC_MACSRCADDRHI_MACSRCADDR3 (0x00FF0000U)
#define EMAC_MACSRCADDRHI_MACSRCADDR3_SHIFT (0x00000010U)

#define EMAC_MACSRCADDRHI_MACSRCADDR4 (0x0000FF00U)
#define EMAC_MACSRCADDRHI_MACSRCADDR4_SHIFT (0x00000008U)

#define EMAC_MACSRCADDRHI_MACSRCADDR5 (0x000000FFU)
#define EMAC_MACSRCADDRHI_MACSRCADDR5_SHIFT (0x00000000U)


/* MACHASH1 */

#define EMAC_MACHASH1_MACHASH1  (0xFFFFFFFFU)
#define EMAC_MACHASH1_MACHASH1_SHIFT (0x00000000U)


/* MACHASH2 */

#define EMAC_MACHASH2_MACHASH2  (0xFFFFFFFFU)
#define EMAC_MACHASH2_MACHASH2_SHIFT (0x00000000U)


/* BOFFTEST */


#define EMAC_BOFFTEST_RNDNUM    (0x03FF0000U)
#define EMAC_BOFFTEST_RNDNUM_SHIFT   (0x00000010U)

#define EMAC_BOFFTEST_COLLCOUNT (0x0000F000U)
#define EMAC_BOFFTEST_COLLCOUNT_SHIFT (0x0000000CU)


#define EMAC_BOFFTEST_TXBACKOFF (0x000003FFU)
#define EMAC_BOFFTEST_TXBACKOFF_SHIFT (0x00000000U)


/* TPACETEST */


#define EMAC_TPACETEST_PACEVAL  (0x0000001FU)
#define EMAC_TPACETEST_PACEVAL_SHIFT (0x00000000U)


/* RXPAUSE */


#define EMAC_RXPAUSE_PAUSETIMER (0x0000FFFFU)
#define EMAC_RXPAUSE_PAUSETIMER_SHIFT (0x00000000U)


/* TXPAUSE */


#define EMAC_TXPAUSE_PAUSETIMER (0x0000FFFFU)
#define EMAC_TXPAUSE_PAUSETIMER_SHIFT (0x00000000U)


/* RXGOODFRAMES */

#define EMAC_RXGOODFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_RXGOODFRAMES_COUNT_SHIFT (0x00000000U)


/* RXBCASTFRAMES */

#define EMAC_RXBCASTFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_RXBCASTFRAMES_COUNT_SHIFT (0x00000000U)


/* RXMCASTFRAMES */

#define EMAC_RXMCASTFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_RXMCASTFRAMES_COUNT_SHIFT (0x00000000U)


/* RXPAUSEFRAMES */

#define EMAC_RXPAUSEFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_RXPAUSEFRAMES_COUNT_SHIFT (0x00000000U)


/* RXCRCERRORS */

#define EMAC_RXCRCERRORS_COUNT  (0xFFFFFFFFU)
#define EMAC_RXCRCERRORS_COUNT_SHIFT (0x00000000U)


/* RXALIGNCODEERRORS */

#define EMAC_RXALIGNCODEERRORS_COUNT (0xFFFFFFFFU)
#define EMAC_RXALIGNCODEERRORS_COUNT_SHIFT (0x00000000U)


/* RXOVERSIZED */

#define EMAC_RXOVERSIZED_COUNT  (0xFFFFFFFFU)
#define EMAC_RXOVERSIZED_COUNT_SHIFT (0x00000000U)


/* RXJABBER */

#define EMAC_RXJABBER_COUNT     (0xFFFFFFFFU)
#define EMAC_RXJABBER_COUNT_SHIFT    (0x00000000U)


/* RXUNDERSIZED */

#define EMAC_RXUNDERSIZED_COUNT (0xFFFFFFFFU)
#define EMAC_RXUNDERSIZED_COUNT_SHIFT (0x00000000U)


/* RXFRAGMENTS */

#define EMAC_RXFRAGMENTS_COUNT  (0xFFFFFFFFU)
#define EMAC_RXFRAGMENTS_COUNT_SHIFT (0x00000000U)


/* RXFILTERED */

#define EMAC_RXFILTERED_COUNT   (0xFFFFFFFFU)
#define EMAC_RXFILTERED_COUNT_SHIFT  (0x00000000U)


/* RXQOSFILTERED */

#define EMAC_RXQOSFILTERED_COUNT (0xFFFFFFFFU)
#define EMAC_RXQOSFILTERED_COUNT_SHIFT (0x00000000U)


/* RXOCTETS */

#define EMAC_RXOCTETS_COUNT     (0xFFFFFFFFU)
#define EMAC_RXOCTETS_COUNT_SHIFT    (0x00000000U)


/* TXGOODFRAMES */

#define EMAC_TXGOODFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_TXGOODFRAMES_COUNT_SHIFT (0x00000000U)


/* TXBCASTFRAMES */

#define EMAC_TXBCASTFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_TXBCASTFRAMES_COUNT_SHIFT (0x00000000U)


/* TXMCASTFRAMES */

#define EMAC_TXMCASTFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_TXMCASTFRAMES_COUNT_SHIFT (0x00000000U)


/* TXPAUSEFRAMES */

#define EMAC_TXPAUSEFRAMES_COUNT (0xFFFFFFFFU)
#define EMAC_TXPAUSEFRAMES_COUNT_SHIFT (0x00000000U)


/* TXDEFERRED */

#define EMAC_TXDEFERRED_COUNT   (0xFFFFFFFFU)
#define EMAC_TXDEFERRED_COUNT_SHIFT  (0x00000000U)


/* TXCOLLISION */

#define EMAC_TXCOLLISION_COUNT  (0xFFFFFFFFU)
#define EMAC_TXCOLLISION_COUNT_SHIFT (0x00000000U)


/* TXSINGLECOLL */

#define EMAC_TXSINGLECOLL_COUNT (0xFFFFFFFFU)
#define EMAC_TXSINGLECOLL_COUNT_SHIFT (0x00000000U)


/* TXMULTICOLL */

#define EMAC_TXMULTICOLL_COUNT  (0xFFFFFFFFU)
#define EMAC_TXMULTICOLL_COUNT_SHIFT (0x00000000U)


/* TXEXCESSIVECOLL */

#define EMAC_TXEXCESSIVECOLL_COUNT (0xFFFFFFFFU)
#define EMAC_TXEXCESSIVECOLL_COUNT_SHIFT (0x00000000U)


/* TXLATECOLL */

#define EMAC_TXLATECOLL_COUNT   (0xFFFFFFFFU)
#define EMAC_TXLATECOLL_COUNT_SHIFT  (0x00000000U)


/* TXUNDERRUN */

#define EMAC_TXUNDERRUN_COUNT   (0xFFFFFFFFU)
#define EMAC_TXUNDERRUN_COUNT_SHIFT  (0x00000000U)


/* TXCARRIERSENSE */

#define EMAC_TXCARRIERSENSE_COUNT (0xFFFFFFFFU)
#define EMAC_TXCARRIERSENSE_COUNT_SHIFT (0x00000000U)


/* TXOCTETS */

#define EMAC_TXOCTETS_COUNT     (0xFFFFFFFFU)
#define EMAC_TXOCTETS_COUNT_SHIFT    (0x00000000U)


/* FRAME64 */

#define EMAC_FRAME64_COUNT      (0xFFFFFFFFU)
#define EMAC_FRAME64_COUNT_SHIFT     (0x00000000U)


/* FRAME65T127 */

#define EMAC_FRAME65T127_COUNT  (0xFFFFFFFFU)
#define EMAC_FRAME65T127_COUNT_SHIFT (0x00000000U)


/* FRAME128T255 */

#define EMAC_FRAME128T255_COUNT (0xFFFFFFFFU)
#define EMAC_FRAME128T255_COUNT_SHIFT (0x00000000U)


/* FRAME256T511 */

#define EMAC_FRAME256T511_COUNT (0xFFFFFFFFU)
#define EMAC_FRAME256T511_COUNT_SHIFT (0x00000000U)


/* FRAME512T1023 */

#define EMAC_FRAME512T1023_COUNT (0xFFFFFFFFU)
#define EMAC_FRAME512T1023_COUNT_SHIFT (0x00000000U)


/* FRAME1024TUP */

#define EMAC_FRAME1024TUP_COUNT (0xFFFFFFFFU)
#define EMAC_FRAME1024TUP_COUNT_SHIFT (0x00000000U)


/* NETOCTETS */

#define EMAC_NETOCTETS_COUNT    (0xFFFFFFFFU)
#define EMAC_NETOCTETS_COUNT_SHIFT   (0x00000000U)


/* RXSOFOVERRUNS */

#define EMAC_RXSOFOVERRUNS_COUNT (0xFFFFFFFFU)
#define EMAC_RXSOFOVERRUNS_COUNT_SHIFT (0x00000000U)


/* RXMOFOVERRUNS */

#define EMAC_RXMOFOVERRUNS_COUNT (0xFFFFFFFFU)
#define EMAC_RXMOFOVERRUNS_COUNT_SHIFT (0x00000000U)


/* RXDMAOVERRUNS */

#define EMAC_RXDMAOVERRUNS_COUNT (0xFFFFFFFFU)
#define EMAC_RXDMAOVERRUNS_COUNT_SHIFT (0x00000000U)


/* MACADDRLO */


#define EMAC_MACADDRLO_VALID    (0x00100000U)
#define EMAC_MACADDRLO_VALID_SHIFT   (0x00000014U)
#define EMAC_MACADDRLO_MATCHFILT (0x00080000U)
#define EMAC_MACADDRLO_MATCHFILT_SHIFT (0x00000013U)
#define EMAC_MACADDRLO_CHANNEL  (0x00070000U)
#define EMAC_MACADDRLO_CHANNEL_SHIFT (0x00000010U)
#define EMAC_MACADDRLO_MACADDR0 (0x0000FF00U)
#define EMAC_MACADDRLO_MACADDR0_SHIFT (0x00000008U)
#define EMAC_MACADDRLO_MACADDR1 (0x000000FFU)
#define EMAC_MACADDRLO_MACADDR1_SHIFT (0x00000000U)


/* MACADDRHI */

#define EMAC_MACADDRHI_MACADDR2 (0xFF000000U)
#define EMAC_MACADDRHI_MACADDR2_SHIFT (0x00000018U)

#define EMAC_MACADDRHI_MACADDR3 (0x00FF0000U)
#define EMAC_MACADDRHI_MACADDR3_SHIFT (0x00000010U)

#define EMAC_MACADDRHI_MACADDR4 (0x0000FF00U)
#define EMAC_MACADDRHI_MACADDR4_SHIFT (0x00000008U)

#define EMAC_MACADDRHI_MACADDR5 (0x000000FFU)
#define EMAC_MACADDRHI_MACADDR5_SHIFT (0x00000000U)


/* MACINDEX */


#define EMAC_MACINDEX_MACINDEX  (0x0000001FU)
#define EMAC_MACINDEX_MACINDEX_SHIFT (0x00000000U)


/* TX0HDP */

#define EMAC_TX0HDP_TX0HDP      (0xFFFFFFFFU)
#define EMAC_TX0HDP_TX0HDP_SHIFT     (0x00000000U)


/* TX1HDP */

#define EMAC_TX1HDP_TX1HDP      (0xFFFFFFFFU)
#define EMAC_TX1HDP_TX1HDP_SHIFT     (0x00000000U)


/* TX2HDP */

#define EMAC_TX2HDP_TX2HDP      (0xFFFFFFFFU)
#define EMAC_TX2HDP_TX2HDP_SHIFT     (0x00000000U)


/* TX3HDP */

#define EMAC_TX3HDP_TX3HDP      (0xFFFFFFFFU)
#define EMAC_TX3HDP_TX3HDP_SHIFT     (0x00000000U)


/* TX4HDP */

#define EMAC_TX4HDP_TX4HDP      (0xFFFFFFFFU)
#define EMAC_TX4HDP_TX4HDP_SHIFT     (0x00000000U)


/* TX5HDP */

#define EMAC_TX5HDP_TX5HDP      (0xFFFFFFFFU)
#define EMAC_TX5HDP_TX5HDP_SHIFT     (0x00000000U)


/* TX6HDP */

#define EMAC_TX6HDP_TX6HDP      (0xFFFFFFFFU)
#define EMAC_TX6HDP_TX6HDP_SHIFT     (0x00000000U)


/* TX7HDP */

#define EMAC_TX7HDP_TX7HDP      (0xFFFFFFFFU)
#define EMAC_TX7HDP_TX7HDP_SHIFT     (0x00000000U)


/* RX0HDP */

#define EMAC_RX0HDP_RX0HDP      (0xFFFFFFFFU)
#define EMAC_RX0HDP_RX0HDP_SHIFT     (0x00000000U)


/* RX1HDP */

#define EMAC_RX1HDP_RX1HDP      (0xFFFFFFFFU)
#define EMAC_RX1HDP_RX1HDP_SHIFT     (0x00000000U)


/* RX2HDP */

#define EMAC_RX2HDP_RX2HDP      (0xFFFFFFFFU)
#define EMAC_RX2HDP_RX2HDP_SHIFT     (0x00000000U)


/* RX3HDP */

#define EMAC_RX3HDP_RX3HDP      (0xFFFFFFFFU)
#define EMAC_RX3HDP_RX3HDP_SHIFT     (0x00000000U)


/* RX4HDP */

#define EMAC_RX4HDP_RX4HDP      (0xFFFFFFFFU)
#define EMAC_RX4HDP_RX4HDP_SHIFT     (0x00000000U)


/* RX5HDP */

#define EMAC_RX5HDP_RX5HDP      (0xFFFFFFFFU)
#define EMAC_RX5HDP_RX5HDP_SHIFT     (0x00000000U)


/* RX6HDP */

#define EMAC_RX6HDP_RX6HDP      (0xFFFFFFFFU)
#define EMAC_RX6HDP_RX6HDP_SHIFT     (0x00000000U)


/* RX7HDP */

#define EMAC_RX7HDP_RX7HDP      (0xFFFFFFFFU)
#define EMAC_RX7HDP_RX7HDP_SHIFT     (0x00000000U)


/* TX0CP */

#define EMAC_TX0CP_TX0CP        (0xFFFFFFFFU)
#define EMAC_TX0CP_TX0CP_SHIFT       (0x00000000U)


/* TX1CP */

#define EMAC_TX1CP_TX1CP        (0xFFFFFFFFU)
#define EMAC_TX1CP_TX1CP_SHIFT       (0x00000000U)


/* TX2CP */

#define EMAC_TX2CP_TX2CP        (0xFFFFFFFFU)
#define EMAC_TX2CP_TX2CP_SHIFT       (0x00000000U)


/* TX3CP */

#define EMAC_TX3CP_TX3CP        (0xFFFFFFFFU)
#define EMAC_TX3CP_TX3CP_SHIFT       (0x00000000U)


/* TX4CP */

#define EMAC_TX4CP_TX4CP        (0xFFFFFFFFU)
#define EMAC_TX4CP_TX4CP_SHIFT       (0x00000000U)


/* TX5CP */

#define EMAC_TX5CP_TX5CP        (0xFFFFFFFFU)
#define EMAC_TX5CP_TX5CP_SHIFT       (0x00000000U)


/* TX6CP */

#define EMAC_TX6CP_TX6CP        (0xFFFFFFFFU)
#define EMAC_TX6CP_TX6CP_SHIFT       (0x00000000U)


/* TX7CP */

#define EMAC_TX7CP_TX7CP        (0xFFFFFFFFU)
#define EMAC_TX7CP_TX7CP_SHIFT       (0x00000000U)


/* RX0CP */

#define EMAC_RX0CP_RX0CP        (0xFFFFFFFFU)
#define EMAC_RX0CP_RX0CP_SHIFT       (0x00000000U)


/* RX1CP */

#define EMAC_RX1CP_RX1CP        (0xFFFFFFFFU)
#define EMAC_RX1CP_RX1CP_SHIFT       (0x00000000U)


/* RX2CP */

#define EMAC_RX2CP_RX2CP        (0xFFFFFFFFU)
#define EMAC_RX2CP_RX2CP_SHIFT       (0x00000000U)


/* RX3CP */

#define EMAC_RX3CP_RX3CP        (0xFFFFFFFFU)
#define EMAC_RX3CP_RX3CP_SHIFT       (0x00000000U)


/* RX4CP */

#define EMAC_RX4CP_RX4CP        (0xFFFFFFFFU)
#define EMAC_RX4CP_RX4CP_SHIFT       (0x00000000U)


/* RX5CP */

#define EMAC_RX5CP_RX5CP        (0xFFFFFFFFU)
#define EMAC_RX5CP_RX5CP_SHIFT       (0x00000000U)


/* RX6CP */

#define EMAC_RX6CP_RX6CP        (0xFFFFFFFFU)
#define EMAC_RX6CP_RX6CP_SHIFT       (0x00000000U)


/* RX7CP */

#define EMAC_RX7CP_RX7CP        (0xFFFFFFFFU)
#define EMAC_RX7CP_RX7CP_SHIFT       (0x00000000U)

/**@}*/
#ifdef __cplusplus
}
#endif

/* USER CODE BEGIN (2) */
/* USER CODE END */

#endif
