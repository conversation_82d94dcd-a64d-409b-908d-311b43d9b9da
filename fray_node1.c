/*
 * fray_node1.c
 *
 *  Created on: Sep 25, 2015
 *      Author: a0324020
 */

#include "fray.h"
#include "LED_Show.h"
#include "sci_common.h"
#include  "file_io.h"
#include "stdio.h"
#include "fray_node_config.h"


#if ((FrayNodeNumber == 1) && (FRAY_ENABLE_FTU == 0))

static void configure_node_header(FRAY_ST *Fray_Ptr);
static void transmit_check_node(FRAY_ST *Fray_Ptr);
static void transmit_static_data(FRAY_ST *Fray_Ptr);
static void transmit_dynamic_data(FRAY_ST *Fray_Ptr);
static void receive_data(FRAY_ST *Fray_Ptr);

void Fray_Test(FRAY_ST *Fray_Ptr);


void Fray_Test(FRAY_ST *Fray_Ptr)
{
	unsigned int index = 0;

#if defined (EXPORT_FRAY_STATUS)
    FILE *fp_er;
#endif

	Fray_testInit(Fray_Ptr);
	configure_node_header(Fray_Ptr);
	Fray_CC_READY(Fray_Ptr, 1, 1);
	Fray_AllowColdStart(Fray_Ptr);
	Fray_StartCommunication(Fray_Ptr);

	while(1)
	{
#if defined (EXPORT_FRAY_STATUS)
		    if(index == 0){           //only save the data in cycle 0
			fp_er = fopen("ccStat02.txt", "w");

			fprintf(fp_er,"Those are Status and Error.\n");
			fprintf(fp_er, "CCSV :   %8X  \n", *(int *)0xFFF7C900);
			fprintf(fp_er, "CCEV :   %8X  \n", *(int *)0xFFF7C904);
			fprintf(fp_er, "SCV  :   %8X  \n", *(int *)0xFFF7C908);
			fprintf(fp_er, "TXRQ1:   %8X  \n", *(int *)0xFFF7CB20);
			fprintf(fp_er, "TXRQ2:   %8X  \n", *(int *)0xFFF7CB24);
			fprintf(fp_er, "TXRQ3:   %8X  \n", *(int *)0xFFF7CB28);
			fprintf(fp_er, "TXRQ4:   %8X  \n", *(int *)0xFFF7CB2C);
			fprintf(fp_er, "EIR  :   %8X  \n", *(int *)0xFFF7C820);
			fprintf(fp_er, "SIR  :   %8X  \n", *(int *)0xFFF7C824);

			fclose(fp_er);
		}
		index = 1;
#endif
	}
}

static void configure_node_header(FRAY_ST *Fray_Ptr)
{
	HEADER_ST Header;
	BC_ST BufCMD;
	HEADER_ST *Header_Ptr = &Header;
	BC_ST *BC_Ptr = &BufCMD;

	// Buffer config initialization
	Header_Ptr->mbi  = 1;   // message buffer interrupt
	Header_Ptr->txm  = 0;   // transmission mode - continuous mode
	Header_Ptr->ppit = 0;   // Payload Preamble Indicator
	Header_Ptr->cfg  = 0;   // message buffer configuration bit (0=RX, 1 = TX)
	Header_Ptr->chb  = 1;   // Ch B
	Header_Ptr->cha  = 1;   // Ch A
	Header_Ptr->cyc  = 0;   // Cycle Filtering Code (no cycle filtering)
	Header_Ptr->fid  = 0;   // Frame ID
	Header_Ptr->plc  = 0;   // Payload Length
	Header_Ptr->dp   = 0;   // Pointer to start of data in message RAM
	Header_Ptr->sfi  = 0;   // startup frame indicator
	Header_Ptr->syn  = 0;   // sync frame indicator

	BC_Ptr->ibrh = 0;  // input buffer number
	BC_Ptr->stxrh= 0;  // set transmission request
	BC_Ptr->ldsh = 0;  // load data section
	BC_Ptr->lhsh = 0;  // load header section
	BC_Ptr->ibsyh = 1; // check for input buffer busy shadow
	BC_Ptr->ibsys = 1; // check for input buffer busy host
	BC_Ptr->obrs = 0;  // output buffer number
	BC_Ptr->rdss = 0;  // read data section
	BC_Ptr->rhss = 0;  // read header section

	// Message buffers
    // Message buffer 0 or 1 is dedicated to hold the startup frame, the sync frame, or the designated single slot
    // frame as configured by SUCC1.TXST, SUCC1.TXSY, and SUCC1.TSM in the SUC Configuration register
    // 1. In this case it can be reconfigured in DEFAULT_CONFIG or CONFIG state only. This ensures that any
    // node transmits at most one startup / sync frame per communication cycle.

// Static Segement MSG
	// Buffer #1, RX MSG
	Header_Ptr->fid  = 1;    // frame ID
	Header_Ptr->dp   = 0x80; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 0;    // RX frame
	Header_Ptr->cyc  = 0x11;  //every 16th cycle at 1/17/33/49 cycle
	Header_Ptr->syn  = 1;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 1;    // Ch B
	Header_Ptr->plc  = 16;
	Header_Ptr->crc  = 0;

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 1;  // input buffer number:0x80, 0x85, 0x8A, 0x8F, 0x94, 0x99, 0x9E, 0xA3

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

	// Buffer #0, TX MSG
	Header_Ptr->fid  = 2;    // frame ID
	Header_Ptr->dp   = 0x88; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 1;    // TX frame
	Header_Ptr->cyc  = 0x00; // all cycles
	Header_Ptr->syn  = 1;    // sync frame indicator
	Header_Ptr->sfi  = 1;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 1;    // Ch B
	Header_Ptr->plc  = 16;   // 32 byte payload
	Header_Ptr->crc  = header_crc_calc(Header_Ptr);

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 0;  // input buffer numberinput buffer number:0x80, 0x85, 0x8A, 0x8F, 0x94, 0x99, 0x9E, 0xA3

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

	// Buffer #3, receive msg
	Header_Ptr->fid  = 7;    // frame ID
	Header_Ptr->dp   = 0x9E; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 0;    // RX frame
	Header_Ptr->cyc  = 0x49;  //every 64th cycle at 9
	Header_Ptr->syn  = 0;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 1;    // Ch B
	Header_Ptr->plc  = 16;
	Header_Ptr->crc  = 0;

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 3;  // input buffer number:0x80, 0x85, 0x8A, 0x8F, 0x94, 0x99, 0x9E, 0xA3

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

	// buffer #2   RX MSG
	Header_Ptr->fid  = 8;   // frame ID
	Header_Ptr->dp   = 0xB8; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 0;    // RX frame
	Header_Ptr->cyc  = 0x18;  //every 16th cycle at 8/24/40/56 cycle
	Header_Ptr->syn  = 0;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 1;    // Ch B
	Header_Ptr->plc  = 16;	 // 24 byte payload
	Header_Ptr->crc  = 0;

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 2;  // input buffer number

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

// Dynamic Segement MSG
	// buffer #5, RX NSG
	Header_Ptr->fid  = 9;     // frame ID
	Header_Ptr->dp   = 0x200; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 0;    // RX frame
	Header_Ptr->cyc  = 0x26;  //every 32th cycle at 6/38 cycle
	Header_Ptr->syn  = 0;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 0;    // Ch B
	Header_Ptr->plc  = 16;
	Header_Ptr->crc  = 0;

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 5;  // input buffer number

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

	// buffer #6, TX MSG
	Header_Ptr->fid  = 15;     // frame ID
	Header_Ptr->dp   = 0x380; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 1;    // TX frame
	Header_Ptr->cyc  = 0x04;  //every 4th cycle at 0/4...cycle
	Header_Ptr->syn  = 0;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 0;    // No transmission on Ch B
	Header_Ptr->plc  = 16;	 // 32 byte payload
	Header_Ptr->crc  = header_crc_calc(Header_Ptr);

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 6;  // input buffer number

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

	// buffer #7, TX MSG
	Header_Ptr->fid  = 16;     // frame ID
	Header_Ptr->dp   = 0x3C0; // Pointer to start of data in message RAM
	Header_Ptr->cfg  = 1;    // TX frame
	Header_Ptr->cyc  = 0x03;  //every 2nd cycle at 1/3/5... cycle
	Header_Ptr->syn  = 0;    // sync frame indicator
	Header_Ptr->sfi  = 0;    // startup frame indicator
	Header_Ptr->cha  = 1;    // Ch A
	Header_Ptr->chb  = 0;    // No transmission on Ch B
	Header_Ptr->plc  = 16;	 // 32 byte payload
	Header_Ptr->crc  = header_crc_calc(Header_Ptr);

	BC_Ptr->lhsh = 1;  // load header section
	BC_Ptr->ibrh = 7;  // input buffer number

	Fray_PrepareHeader(Fray_Ptr, Header_Ptr);
	Fray_Transmit(Fray_Ptr, BC_Ptr);          //transfer header and data to FRAY MSG RAM

}

static void transmit_static_data(FRAY_ST *Fray_Ptr)
{
	BC_ST INBUF;    //input buffer cmd
	BC_ST *IB_Ptr = &INBUF;    //input buffer cmd

	unsigned int i;
	gio_LED_Blinky(0);

	IB_Ptr->ibrh  = 0;  // input buffer number
	IB_Ptr->stxrh = 1;  // set transmission request
	IB_Ptr->ldsh  = 1;  // load data section
	IB_Ptr->lhsh  = 0;  // load header section
	IB_Ptr->ibsys = 0; // check for input buffer busy shadow
	IB_Ptr->ibsyh = 1; // check for input buffer busy host

	// write payload for buffers
	// buffer #0, FID=2
	IB_Ptr->ibrh  = 0;  // input buffer number
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x12121212);    //node 1, slote 2
	Fray_Transmit(Fray_Ptr, IB_Ptr);
}

static void transmit_dynamic_data(FRAY_ST *Fray_Ptr)
{
	BC_ST INBUF;    //input buffer cmd
	BC_ST *IB_Ptr = &INBUF;    //input buffer cmd

	unsigned int i;
	gio_LED_Blinky(1);

	IB_Ptr->ibrh  = 0;  // input buffer number
	IB_Ptr->stxrh = 1;  // set transmission request
	IB_Ptr->ldsh  = 1;  // load data section
	IB_Ptr->lhsh  = 0;  // load header section
	IB_Ptr->ibsys = 0; // check for input buffer busy shadow
	IB_Ptr->ibsyh = 1; // check for input buffer busy host

	// write payload for buffers

	// write payload for buffers
	// buffer #3, FID=15
	IB_Ptr->ibrh  = 6;  // input buffer number
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x1F1F1F1F);    // Node 1, Slote 15
	Fray_Transmit(Fray_Ptr, IB_Ptr);

	// buffer #5, fid=16
	IB_Ptr->ibrh = 7;
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x10101010);    // Node 1, Slote 16
	Fray_Transmit(Fray_Ptr, IB_Ptr);
}

static void receive_data(FRAY_ST *Fray_Ptr)
{
	BC_ST OUTBUF;    //output buffer cmd
	BC_ST *OB_Ptr = &OUTBUF;    //output buffer cmd

	unsigned int  ndat1;
	// check received frames
    ndat1 = Fray_Ptr->NDAT1_UN.NDAT1_UL;

    if ((ndat1 & (0x1 << 1)) != 0) //buffer 1 from Node 0, slot  1
    {
      OB_Ptr->obrs=1;  // output buffer number
      OB_Ptr->rdss=1;  // read data section
      OB_Ptr->rhss=0;  // read header section
      // Transfer message buffer 1 data to output buffer registers
      Fray_Receive(Fray_Ptr, OB_Ptr);
      if (Fray_Ptr->RDDS[0] != 0x01010101)
         gio_LED_Blinky(4);  //5: from Node 1
	}
    if (ndat1 & (0x1 << 3)) //buffer 3 from Node 3, slot  7
    {
        OB_Ptr->obrs=3;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[0] != 0x37373737)
            gio_LED_Blinky(7);  //5: from Node 1
    }
    if (ndat1 & (0x1 << 2)) //buffer 2 from Node 2, slot  8
    {
        OB_Ptr->obrs=2;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[0] != 0x28282828)
            gio_LED_Blinky(6);  //5: from Node 1
    }
    if (ndat1 & (0x1 << 5)) //buffer 5 from Node 0, slot  9
    {
        OB_Ptr->obrs=5;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[0] != 0x09090909)
           gio_LED_Blinky(4);  //5: from Node 1
    }
}

static void transmit_check_node(FRAY_ST *Fray_Ptr)
{
	BC_ST INBUF;    //input buffer cmd
	BC_ST OUTBUF;    //output buffer cmd

	BC_ST *IB_Ptr = &INBUF;    //input buffer cmd
	BC_ST *OB_Ptr = &OUTBUF;    //output buffer cmd

	unsigned int  ndat1, i;

	IB_Ptr->ibrh  = 0;  // input buffer number
	IB_Ptr->stxrh = 1;  // set transmission request
	IB_Ptr->ldsh  = 1;  // load data section
	IB_Ptr->lhsh  = 0;  // load header section
	IB_Ptr->ibsys = 0; // check for input buffer busy shadow
	IB_Ptr->ibsyh = 1; // check for input buffer busy host

    // wait for cycle start interrupt flag
    Fray_Ptr->SIR_UN.SIR_UL = 0xFFFFFFFF;            // clear all status int. flags
    while ((Fray_Ptr->SIR_UN.SIR_UL & 0x4) == 0x0);    // wait for CYCS interrupt flag
    Fray_Ptr->SIR_UN.SIR_UL = 0xFFFFFFFF;            // clear all status int. flags

	// write payload for buffers
	// buffer #0, FID=2
	IB_Ptr->ibrh  = 0;  // input buffer number
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x12121212);    //node 1, slote 2
	Fray_Transmit(Fray_Ptr, IB_Ptr);

	// write payload for buffers
	// buffer #3, FID=15
	IB_Ptr->ibrh  = 6;  // input buffer number
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x1F1F1F1F);    // Node 1, Slote 15
	Fray_Transmit(Fray_Ptr, IB_Ptr);

	// buffer #5, fid=16
	IB_Ptr->ibrh = 7;
	for (i=0; i<8; i++)
    	(Fray_Ptr->WRDS[i] = 0x10101010);    // Node 1, Slote 16
	Fray_Transmit(Fray_Ptr, IB_Ptr);

	// check received frames
    ndat1 = Fray_Ptr->NDAT1_UN.NDAT1_UL;

    if ((ndat1 & (0x1 << 1)) != 0) //buffer 1 from Node 0, slot  1
    {
      OB_Ptr->obrs=1;  // output buffer number
      OB_Ptr->rdss=1;  // read data section
      OB_Ptr->rhss=0;  // read header section
      // Transfer message buffer 1 data to output buffer registers
      Fray_Receive(Fray_Ptr, OB_Ptr);
      if (Fray_Ptr->RDDS[1] != 0x01010101)
    	  PrintString("\n\rNode 1 got wrong data from Node 0, Slot 1\n");
      gio_LED_Blinky(4);  //5: from Node 1
	}
    else if (ndat1 & (0x1 << 3)) //buffer 3 from Node 3, slot  7
    {
        OB_Ptr->obrs=3;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[1] != 0x37373737)
      	  PrintString("\n\rNode 1 got wrong data from Node 3, Slot 7\n");
        gio_LED_Blinky(7);  //5: from Node 1
    }
    else if (ndat1 & (0x1 << 2)) //buffer 2 from Node 2, slot  8
    {
        OB_Ptr->obrs=2;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[1] != 0x28282828)
      	  PrintString("\n\rNode 1 got wrong data from Node 2, Slot 8\n");
        gio_LED_Blinky(6);  //5: from Node 1
    }
    else if (ndat1 & (0x1 << 5)) //buffer 5 from Node 0, slot  9
    {
        OB_Ptr->obrs=5;  // output buffer number
        OB_Ptr->rdss=1;  // read data section
        OB_Ptr->rhss=0;  // read header section
        // Transfer message buffer 1 data to output buffer registers
        Fray_Receive(Fray_Ptr, OB_Ptr);
        if (Fray_Ptr->RDDS[1] != 0x09090909)
      	  PrintString("\n\rNode 1 got wrong data from Node 0, Slot 9\n");
        gio_LED_Blinky(4);  //5: from Node 1
    }
}


#pragma WEAK(frayErrorNotification)
void frayErrorNotification(FRAY_ST *Fray_PST, unsigned int notification)
{

}

#pragma WEAK(frayStatusChangeNotification)
void frayStatusChangeNotification(FRAY_ST *Fray_Ptr, unsigned int notification)
{
	if (notification == ( 0x1 << 2))   //CYCS
	{
		transmit_static_data(Fray_Ptr);
	}

	if (notification == ( 0x1 << 4))   //RXI
	{
		receive_data(Fray_Ptr);
	}

	if (notification == ( 0x1 << 15))   //SDS
	{
		transmit_dynamic_data(Fray_Ptr);
	}

}

#endif
