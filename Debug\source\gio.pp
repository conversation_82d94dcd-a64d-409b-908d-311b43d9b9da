# FIXED

source/gio.obj: ../source/gio.c
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/gio.h
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h
source/gio.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h
source/gio.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h

../source/gio.c: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/gio.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h: 
