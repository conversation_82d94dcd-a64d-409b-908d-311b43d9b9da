/** @file sys_main.c 
*   @brief Application main file
*   @date 04.October.2011
*   @version 1.02.000
*
*   This file contains an empty main function,
*   which can be used for the application.
*/

/* (c) Texas Instruments 2009-2011, All rights reserved. */

/* USER CODE BEGIN (0) */
/* USER CODE END */

/* Include Files */
#include "sys_common.h"
#include "system.h"

/* USER CODE BEGIN (1) */
#include "fray.h"
#include "ftu.h"

#include "gio.h"
#include "sci.h"
#include "sci_common.h"
#include "fray_node_config.h"
#include "sys_core.h"

/* USER CODE END */

/** @fn void main(void)
*   @brief Application main function
*   @note This function is empty by default.
*
*   This function is called after startup.
*   The user can use this function to implement the application.
*/

/* USER CODE BEGIN (2) */

/////////////Data definition////////////////////////


/* Test mode is used to check the FlexRay Message RAM*/

#if (FRAY_ENABLE_FTU == 0)
extern void Fray_Test(FRAY_ST *Fray_Ptr);
#else
extern void FTU_Test(FRAY_ST *Fray_Ptr, FTU_ST *FTU_Ptr);

#endif

/* USER CODE END */

void main(void)
{
/* USER CODE BEGIN (3) */
	gioInit();
	sciInit();
	_enable_IRQ();
	_coreEnableVfp_();

	//	asm(" b #-4");
	PrintString("\n\rFlexray test: Node 0");

//FRAY_ENABLE_FTU is defined in fray_node_config.h
#if (FRAY_ENABLE_FTU == 0)
   	Fray_Test(frayREG1);
#else
   	FTU_Test(frayREG1, ftuREG);
#endif

/* USER CODE END */
}


/* USER CODE BEGIN (4) */
/* USER CODE END */
