/*
 * fray_ram.h
 *
 *  Created on: Sep 24, 2015
 *      Author: a0324020
 */

#ifndef FRAY_RAM_H_
#define FRAY_RAM_H_

#define FRAY_PAYLOAD  0x8

typedef volatile struct fray_ram_header
{
	union h1
	{
		unsigned long HEADER1_UL;
		struct
		{
			unsigned : 2;
			unsigned MBI : 1;
			unsigned TXM : 1;
			unsigned NME : 1;
			unsigned CFG : 1;
			unsigned CHB : 1;
			unsigned CHA : 1;
			unsigned : 1;
			unsigned Cycle_Code: 7;
			unsigned : 5;
			unsigned FrameID : 11;
		} HEADER1_ST;
	} HEADER1_UN;

	union h2
	{
		unsigned long HEADER2_UL;
		struct
		{
			unsigned : 1;
			unsigned PLR : 7;
			unsigned : 1;
			unsigned PLC : 7;
			unsigned : 5;
			unsigned crc : 11;
		} HEADER2_ST;
	} HEADER2_UN;

	union h3
	{
		unsigned long HEADER3_UL;
		struct
		{
			unsigned : 2;
			unsigned RES : 1;
			unsigned PPI : 1;
			unsigned NFI : 1;
			unsigned SYN : 1;
			unsigned SFI : 1;
			unsigned RCI : 1;
			unsigned : 2;
			unsigned RCcnt : 6;
			unsigned : 5;
			unsigned Data_Ptr : 11;
		} HEADER3_ST;
	} HEADER3_UN;

	union h4
	{
		unsigned long HEADER4_UL;
		struct
		{
			unsigned : 19;
			unsigned MLST : 1;
			unsigned ESB : 1;
			unsigned ESA : 1;
			unsigned TCIB : 1;
			unsigned TCIA : 1;
			unsigned SVOB : 1;
			unsigned SVOA : 1;
			unsigned CEOB : 1;
			unsigned CEOA : 1;
			unsigned SEOB : 1;
			unsigned SEOA : 1;
			unsigned VFRB : 1;
			unsigned VFRA1 : 1;
		} HEADER4_ST;
	} HEADER4_UN;
}FRAY_RAM_ST;



typedef volatile struct fram_ram_msg
{
	FRAY_RAM_ST FRAY_HEADER;
	unsigned int FRAY_DATA[FRAY_PAYLOAD];
}FRAY_MSG;


#endif /* FRAY_RAM_HEADER_H_ */
