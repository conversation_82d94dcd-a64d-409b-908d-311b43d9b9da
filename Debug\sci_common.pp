# FIXED

sci_common.obj: ../sci_common.c
sci_common.obj: ../sci_common.h
sci_common.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sci.h
sci_common.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_sci.h
sci_common.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h
sci_common.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h
sci_common.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h
sci_common.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/string.h

../sci_common.c: 
../sci_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sci.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_sci.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/string.h: 
