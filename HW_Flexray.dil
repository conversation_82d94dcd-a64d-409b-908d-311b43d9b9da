# TMS570LS3137ZWT 10/09/15 15:16:25
# 
ARCH=TMS570LS3137ZWT
# 
DRIVER.TOOLS.VAR.GCC.VALUE=0
DRIVER.TOOLS.VAR.ARM.VALUE=0
DRIVER.TOOLS.VAR.IAR.VALUE=0
DRIVER.TOOLS.VAR.GHS.VALUE=0
DRIVER.TOOLS.VAR.TI.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_PERMISSION.VALUE=PRIV_RW_USER_RW_NOEXEC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.VIM_CHANNEL_122_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_114_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_106_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_70_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_62_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_54_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_46_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_38_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.PMM_MEM_PD3_STATE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CAPTURE_EVENT_SOURCE_0.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_PERMISSION_VALUE.VALUE=0x0300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_81_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_73_NAME.VALUE=het2LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_65_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_57_NAME.VALUE=adc2Group2Interrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_49_NAME.VALUE=spi4HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_2_MAPPING.VALUE=2
DRIVER.SYSTEM.VAR.VIM_CAPTURE_EVENT_SOURCE_1.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_111_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_103_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_ADC1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.EQEP2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_MEMINIT_SELECTED.VALUE=1
DRIVER.SYSTEM.VAR.FLASH_DATA_3_WAIT_STATE_FREQ.VALUE=180.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_96_MAPPING.VALUE=96
DRIVER.SYSTEM.VAR.VIM_CHANNEL_88_MAPPING.VALUE=88
DRIVER.SYSTEM.VAR.VIM_CHANNEL_1_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_END_ADDRESS.VALUE=0xfcffffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_5_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_DATA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_11_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_STC_SELFCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SPI3_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL1_BYPASS_ON_SLIP.VALUE=0x20000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_124_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_116_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_108_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SIZE.VALUE=256_KB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_100_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_98_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_20_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_12_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_2_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI2_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CRC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.MIBSPI1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_HCLK_FREQ.VALUE=180.000
DRIVER.SYSTEM.VAR.CLKT_PLL2_FREQ.VALUE=80.00
DRIVER.SYSTEM.VAR.VIM_CHANNEL_81_MAPPING.VALUE=81
DRIVER.SYSTEM.VAR.VIM_CHANNEL_73_MAPPING.VALUE=73
DRIVER.SYSTEM.VAR.VIM_CHANNEL_65_MAPPING.VALUE=65
DRIVER.SYSTEM.VAR.VIM_CHANNEL_57_MAPPING.VALUE=57
DRIVER.SYSTEM.VAR.VIM_CHANNEL_49_MAPPING.VALUE=49
DRIVER.SYSTEM.VAR.VIM_CHANNEL_40_NAME.VALUE=dmaBTCAInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_32_NAME.VALUE=frayLowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_24_NAME.VALUE=het1LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_16_NAME.VALUE=can1HighLevelInterrupt
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_6_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_92_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_84_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_76_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_68_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_31_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_23_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_15_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_CCM_SELFCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.PMM_MEM_PD2_STATE_AVAIL.VALUE=1
DRIVER.SYSTEM.VAR.ECLK_CLKSRC.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.CLKT_PLL2_OUTPUT_DIV.VALUE=6
DRIVER.SYSTEM.VAR.CLKT_EXT2_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CLKT_PLL1_SOURCE_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_END_ADDRESS.VALUE=0x63ffffff
DRIVER.SYSTEM.VAR.VIM_CHANNEL_96_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_88_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_9_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_4_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HET2_DP_PBISTCHECK_ENA.VALUE=0x00040000
DRIVER.SYSTEM.VAR.CLKT_RTI2_PRE_SOURCE.VALUE=PLL1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SIZE_VALUE.VALUE=0x1A
DRIVER.SYSTEM.VAR.VIM_CHANNEL_99_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_50_MAPPING.VALUE=50
DRIVER.SYSTEM.VAR.VIM_CHANNEL_42_MAPPING.VALUE=42
DRIVER.SYSTEM.VAR.VIM_CHANNEL_34_MAPPING.VALUE=34
DRIVER.SYSTEM.VAR.VIM_CHANNEL_26_MAPPING.VALUE=26
DRIVER.SYSTEM.VAR.VIM_CHANNEL_18_MAPPING.VALUE=18
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD2_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.CLKT_VCLK3_DOMAIN_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_ECC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_BANKS.VALUE=4
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_IRQ_DISP_ENTRY.VALUE=_irqDispatch
DRIVER.SYSTEM.VAR.VIM_CHANNEL_111_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_103_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_92_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_84_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_76_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_68_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CAN3_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK1_SOURCE.VALUE=VCLK
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.ETPWM4_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.DCC2_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_PLL1_RESET_ON_OSCILLATOR_FAIL.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_PERMISSION_VALUE.VALUE=0x0600
DRIVER.SYSTEM.VAR.VIM_CHANNEL_11_MAPPING.VALUE=11
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_PERMISSION.VALUE=PRIV_RW_USER_RW_EXEC
DRIVER.SYSTEM.VAR.LBIST_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK2_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_END_ADDRESS.VALUE=0x003fffff
DRIVER.SYSTEM.VAR.VIM_CHANNEL_41_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_33_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_25_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_17_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_3_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ECAP6_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SCI_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.FLASH_DATA_1_WAIT_STATE_FREQ.VALUE=90.0
DRIVER.SYSTEM.VAR.RAM_STACK_IRQ_BASE.VALUE=0x08001200
DRIVER.SYSTEM.VAR.VIM_CHANNEL_125_MAPPING.VALUE=125
DRIVER.SYSTEM.VAR.VIM_CHANNEL_117_MAPPING.VALUE=117
DRIVER.SYSTEM.VAR.VIM_CHANNEL_109_MAPPING.VALUE=109
DRIVER.SYSTEM.VAR.VIM_CHANNEL_90_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_82_NAME.VALUE=dcc1DoneInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_74_NAME.VALUE=sciLowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_66_NAME.VALUE=i2cInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_58_NAME.VALUE=ftuErrorInterrupt
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_DOMAIN_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_PMU_GLOBAL_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_BASE_ADDRESS.VALUE=0xFF000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_97_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_89_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_81_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_73_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_70_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_65_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_62_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_57_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_54_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_49_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_46_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_38_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD2_STATE.VALUE=1
DRIVER.SYSTEM.VAR.EMAC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_PBIST_DP_SELECTED.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_8_MAPPING.VALUE=8
DRIVER.SYSTEM.VAR.VIM_CHANNEL_2_NAME.VALUE=rtiCompare0Interrupt
DRIVER.SYSTEM.VAR.CORE_PMU_COUNTER0_EVENT.VALUE=0x11
DRIVER.SYSTEM.VAR.FLASH_ADDRESS_WAIT_STATES.VALUE=1
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_RESET_ENTRY.VALUE=_c_int00
DRIVER.SYSTEM.VAR.VIM_CHANNEL_127_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_122_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_119_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_114_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_106_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_9_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.ADC1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.MIBSPI_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ECLK_VCLK1_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL_FREQ.VALUE=00.0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SIZE_VALUE.VALUE=0x0A
DRIVER.SYSTEM.VAR.VIM_CHANNEL_125_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_117_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_110_MAPPING.VALUE=110
DRIVER.SYSTEM.VAR.VIM_CHANNEL_109_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_102_MAPPING.VALUE=102
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.RAM_LENGTH.VALUE=0x00040000
DRIVER.SYSTEM.VAR.CLKT_VCLK1_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SIZE.VALUE=64_MB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_124_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_116_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_108_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_ESRAM_SP_PBISTCHECK_VALUE_NEW.VALUE=0x08300020
DRIVER.SYSTEM.VAR.VIM_CHANNEL_41_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_33_NAME.VALUE=dmaFTCAInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_25_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_17_NAME.VALUE=spi2HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_1_MAPPING.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL_SOURCE_ENABLE.VALUE=0x00000008
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_END_ADDRESS.VALUE=0x203fffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_BASE_ADDRESS.VALUE=0xF0000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_40_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_32_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_24_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_16_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_5_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_PARITY_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.SAFETY_INIT_FRAY_DP_PBISTCHECK_ENA.VALUE=0x00008000
DRIVER.SYSTEM.VAR.PBIST_ERRATA_4_FMTM.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_TYPE_VALUE.VALUE=0x0000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_95_MAPPING.VALUE=95
DRIVER.SYSTEM.VAR.VIM_CHANNEL_87_MAPPING.VALUE=87
DRIVER.SYSTEM.VAR.VIM_CHANNEL_79_MAPPING.VALUE=79
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_4_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_IRQ_VIC_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_121_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_113_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_105_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_70_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_62_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_54_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_50_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_46_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_42_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_38_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_34_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_26_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_18_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI1_DP_PBISTCHECK_ENA.VALUE=0x00000040
DRIVER.SYSTEM.VAR.SPI1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_100_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CLKT_RTI2_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.RAM_ECC_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_7_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_99_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN4_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SCI_ALL_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL_FREQ_INPUT.VALUE=16.0
DRIVER.SYSTEM.VAR.STC_INTERVAL.VALUE=24
DRIVER.SYSTEM.VAR.CLKT_LPO_HIGH_TRIM_VALUE.VALUE=16
DRIVER.SYSTEM.VAR.CLKT_GCLK_FREQ.VALUE=180.000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_PERMISSION_VALUE.VALUE=0x1000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_80_MAPPING.VALUE=80
DRIVER.SYSTEM.VAR.VIM_CHANNEL_72_MAPPING.VALUE=72
DRIVER.SYSTEM.VAR.VIM_CHANNEL_64_MAPPING.VALUE=64
DRIVER.SYSTEM.VAR.VIM_CHANNEL_56_MAPPING.VALUE=56
DRIVER.SYSTEM.VAR.VIM_CHANNEL_48_MAPPING.VALUE=48
DRIVER.SYSTEM.VAR.CLKT_PLL1_REF_CLOCK_DIV.VALUE=6
DRIVER.SYSTEM.VAR.FLASHW_BASE_ADDRESS.VALUE=0xFFF87000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_FIQ_ENTRY.VALUE="ldr pc,[pc,#-0x1b0]"
DRIVER.SYSTEM.VAR.VIM_CHANNEL_10_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN5_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.SCILIN_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SPI_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ALL_DVR_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CCM_MENU_VALUE.VALUE=0x0001
DRIVER.SYSTEM.VAR.PBIST_ENA1.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_VCLK4_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.VIM_CHANNEL_97_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_89_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_OSCILLATOR_FREQ.VALUE=16.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_91_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_83_NAME.VALUE=dcc2DoneInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_75_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_67_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_59_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_41_MAPPING.VALUE=41
DRIVER.SYSTEM.VAR.VIM_CHANNEL_33_MAPPING.VALUE=33
DRIVER.SYSTEM.VAR.VIM_CHANNEL_25_MAPPING.VALUE=25
DRIVER.SYSTEM.VAR.VIM_CHANNEL_17_MAPPING.VALUE=17
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_IRQ_MODE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_11_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_11_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.PMM_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.EMIF_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CAN1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CAN_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_3_NAME.VALUE=rtiCompare1Interrupt
DRIVER.SYSTEM.VAR.PMM_MEM_PD3_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.CLKT_PLL1_OUTPUT_DIV.VALUE=2
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_PERMISSION.VALUE=PRIV_RW_USER_RW_NOEXEC
DRIVER.SYSTEM.VAR.CLKT_PLL2_FM_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_BASE_ADDRESS.VALUE=0x08400000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_92_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_91_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_84_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_83_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_76_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_75_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_68_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_67_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_59_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_8_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_STC_CPUSELFTEST_ENA.VALUE=0
DRIVER.SYSTEM.VAR.ETPWM2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.HET1_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_RTI1_PRE_SOURCE.VALUE=PLL1
DRIVER.SYSTEM.VAR.FLASH_MODE_VALUE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SIZE_VALUE.VALUE=0x19
DRIVER.SYSTEM.VAR.VIM_CHANNEL_126_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_118_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_10_MAPPING.VALUE=10
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SIZE.VALUE=128_MB
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_125_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_117_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_109_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_91_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_83_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_75_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_67_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_59_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_3_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HET1_DP_PBISTCHECK_ENA.VALUE=0x00001000
DRIVER.SYSTEM.VAR.ECAP4_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL2_BYPASS_ON_SLIP.VALUE=0x20000000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_124_MAPPING.VALUE=124
DRIVER.SYSTEM.VAR.VIM_CHANNEL_116_MAPPING.VALUE=116
DRIVER.SYSTEM.VAR.VIM_CHANNEL_108_MAPPING.VALUE=108
DRIVER.SYSTEM.VAR.VIM_CHANNEL_50_NAME.VALUE=adc2Group0Interrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_42_NAME.VALUE=can2LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_34_NAME.VALUE=dmaLFSAInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_26_NAME.VALUE=mibspi1LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_18_NAME.VALUE=frayHighLevelInterrupt
DRIVER.SYSTEM.VAR.FLASH_ARBITRATION.VALUE=FIX
DRIVER.SYSTEM.VAR.CLKT_LPO_HIGH_SOURCE_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_110_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_102_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PBIST_ALGO_9_10.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL2_FREQ_INPUT.VALUE=16.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_7_MAPPING.VALUE=7
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_PERMISSION.VALUE=PRIV_RW_USER_RW_EXEC
DRIVER.SYSTEM.VAR.CLKT_RTI2_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_BACKGROUND_REGION_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_8_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_2_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CONFIG.VALUE=TRUE
DRIVER.SYSTEM.VAR.VIM_CHANNEL_101_MAPPING.VALUE=101
DRIVER.SYSTEM.VAR.VIM_CHANNEL_101_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.RAM_STACK_ABORT_LENGTH.VALUE=0x00000100
DRIVER.SYSTEM.VAR.FLASH_DATA_MAX_WAIT_STATES.VALUE=3
DRIVER.SYSTEM.VAR.FLASH_MODE.VALUE=PIPELINE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_7_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_61_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_61_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_53_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_53_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_45_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_45_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_40_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_37_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_37_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_32_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_29_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_29_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_24_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_16_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI4_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_EMAC_SP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.MINIT_VALUE.VALUE=0x1E57F
DRIVER.SYSTEM.VAR.VIM_CHANNEL_0_MAPPING.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL1_DIV.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_VCLK4_DOMAIN_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL2_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.RAM_BASE_ADDRESS.VALUE=0x08000000
DRIVER.SYSTEM.VAR.CORE_PMU_EVENT_EXPORT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_126_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_118_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_110_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_102_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_96_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_88_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.GIO_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SIZE_VALUE.VALUE=0x17
DRIVER.SYSTEM.VAR.VIM_CHANNEL_94_MAPPING.VALUE=94
DRIVER.SYSTEM.VAR.VIM_CHANNEL_86_MAPPING.VALUE=86
DRIVER.SYSTEM.VAR.VIM_CHANNEL_78_MAPPING.VALUE=78
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_TYPE.VALUE=STRONGLYORDERED_SHAREABLE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_3_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_BASE_ADDRESS.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_UNDEF_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_8_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_PBIST_SP_SELECTED.VALUE=0
DRIVER.SYSTEM.VAR.RAM_STACK_ABORT_BASE.VALUE=0x08001300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_92_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_84_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_76_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_68_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CLKT_RTI1_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_6_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_123_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_115_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_107_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_31_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_23_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_15_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_1_INT_TYPE.VALUE=FIQ
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD3_STATE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_EFUSE_SELFCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_GHV_WAKUP_SOURCE.VALUE=OSC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_TYPE_VALUE.VALUE=0x0000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_71_MAPPING.VALUE=71
DRIVER.SYSTEM.VAR.VIM_CHANNEL_63_MAPPING.VALUE=63
DRIVER.SYSTEM.VAR.VIM_CHANNEL_55_MAPPING.VALUE=55
DRIVER.SYSTEM.VAR.VIM_CHANNEL_47_MAPPING.VALUE=47
DRIVER.SYSTEM.VAR.VIM_CHANNEL_39_MAPPING.VALUE=39
DRIVER.SYSTEM.VAR.VIM_CHANNEL_4_NAME.VALUE=rtiCompare2Interrupt
DRIVER.SYSTEM.VAR.CORE_PMU_COUNTER1_EVENT.VALUE=0x11
DRIVER.SYSTEM.VAR.EFUSE_SELFTEST_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_DOMAIN_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.RAM_LINK_BASE_ADDRESS.VALUE=0x08001500
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_120_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_112_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_104_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_30_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_22_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_14_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_127_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_119_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CLKT_PLL2_DIV.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_VCLK3_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_LPO_TRIM_OTP_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SIZE.VALUE=8_MB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_95_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_87_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_79_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_61_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_53_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_45_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_37_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_29_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_VIM1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_SOURCE.VALUE=VCLK
DRIVER.SYSTEM.VAR.FLASH_ADDRESS_WAIT_STATES_FREQ.VALUE=120.0
DRIVER.SYSTEM.VAR.RAM_STACK_BASE.VALUE=0x08000000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_51_NAME.VALUE=adc2Group1Interrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_43_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_40_MAPPING.VALUE=40
DRIVER.SYSTEM.VAR.VIM_CHANNEL_35_NAME.VALUE=can2HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_32_MAPPING.VALUE=32
DRIVER.SYSTEM.VAR.VIM_CHANNEL_27_NAME.VALUE=linLowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_24_MAPPING.VALUE=24
DRIVER.SYSTEM.VAR.VIM_CHANNEL_19_NAME.VALUE=crcInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_16_MAPPING.VALUE=16
DRIVER.SYSTEM.VAR.CLKT_VCLK3_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_END_ADDRESS.VALUE=0xf07fffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.ETPWM7_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_1.VALUE=1
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_2.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_96_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_88_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN4_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_3.VALUE=1
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_4.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_110_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_102_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_5.VALUE=1
DRIVER.SYSTEM.VAR.LBIST_STT.VALUE=1
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_6.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_HTU2_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_7.VALUE=1
DRIVER.SYSTEM.VAR.ECAP2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_8.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_TYPE_VALUE.VALUE=0x0010
DRIVER.SYSTEM.VAR.VIM_CHANNEL_123_MAPPING.VALUE=123
DRIVER.SYSTEM.VAR.VIM_CHANNEL_115_MAPPING.VALUE=115
DRIVER.SYSTEM.VAR.VIM_CHANNEL_107_MAPPING.VALUE=107
DRIVER.SYSTEM.VAR.VIM_CHANNEL_10_NAME.VALUE=het1HighLevelInterrupt
DRIVER.SYSTEM.VAR.PMM_MEM_PD2_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_9.VALUE=1
DRIVER.SYSTEM.VAR.RAM_STACK_USER_LENGTH.VALUE=0x00001000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_PERMISSION.VALUE=PRIV_RW_USER_RW_NOEXEC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_END_ADDRESS.VALUE=0x0843ffff
DRIVER.SYSTEM.VAR.VIM_CHANNEL_80_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_72_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_64_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_56_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_48_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_10_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_7_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_USB_SP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.SAFETY_INIT_PBIST_SELFCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.PBIST_ERRATA_4_STCROMDATA.VALUE=0x0A63FFFF
DRIVER.SYSTEM.VAR.SCI2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.LIN_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_RTI1_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SIZE_VALUE.VALUE=0x11
DRIVER.SYSTEM.VAR.VIM_CHANNEL_6_MAPPING.VALUE=6
DRIVER.SYSTEM.VAR.CLKT_PLL2_SPEADING_AMOUNT.VALUE=61
DRIVER.SYSTEM.VAR.CLKT_PLL2_SPEADING_RATE.VALUE=255
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_BASE_ADDRESS.VALUE=0x08001000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_TYPE.VALUE=STRONGLYORDERED_SHAREABLE
DRIVER.SYSTEM.VAR.VIM_CHANNEL_121_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_113_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_105_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_90_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_90_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_82_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_82_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_74_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_74_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_66_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_66_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_58_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_58_INT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI5_DP_PBISTCHECK_ENA.VALUE=0x00000100
DRIVER.SYSTEM.VAR.CLKT_PLL2_RESET_ON_SLIP.VALUE=0x00000000
DRIVER.SYSTEM.VAR.ECLK_FREQ.VALUE=2.000
DRIVER.SYSTEM.VAR.CLKT_AVCLK1_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_100_MAPPING.VALUE=100
DRIVER.SYSTEM.VAR.VIM_CHANNEL_93_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_85_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_77_NAME.VALUE=EMACTxIntISR
DRIVER.SYSTEM.VAR.VIM_CHANNEL_69_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.RAM_STACK_IRQ_LENGTH.VALUE=0x00000100
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_6_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_2_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_RTI2_POST_SOURCE.VALUE=VCLK
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_PERMISSION_VALUE.VALUE=0x1300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_5_NAME.VALUE=rtiCompare3Interrupt
DRIVER.SYSTEM.VAR.RAM_STACK_LENGTH.VALUE=0x00001500
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_PERMISSION.VALUE=PRIV_RO_USER_RO_EXEC
DRIVER.SYSTEM.VAR.CLKT_LPO_BIAS.VALUE=true
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_DIVIDER1.VALUE=4
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_END_ADDRESS.VALUE=0xffffffff
DRIVER.SYSTEM.VAR.CORE_PRAGMA_ENA.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_101_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_4_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_1_INT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SPI4_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_POST_SOURCE.VALUE=PLL2_ODCLK_8
DRIVER.SYSTEM.VAR.CLKT_VCLK1_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_FREQ1.VALUE=90.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_93_MAPPING.VALUE=93
DRIVER.SYSTEM.VAR.VIM_CHANNEL_85_MAPPING.VALUE=85
DRIVER.SYSTEM.VAR.VIM_CHANNEL_77_MAPPING.VALUE=77
DRIVER.SYSTEM.VAR.VIM_CHANNEL_69_MAPPING.VALUE=69
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_FREQ2.VALUE=90.0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SIZE.VALUE=16_MB
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_BASE_ADDRESS.VALUE=0xFC000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_2_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_60_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_52_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_44_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_41_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_36_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_33_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_28_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_25_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_17_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.CLKT_PLL1_MUL.VALUE=135
DRIVER.SYSTEM.VAR.VIM_CHANNEL_60_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_52_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_44_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_36_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_28_NAME.VALUE=adc1Group2Interrupt
DRIVER.SYSTEM.VAR.CLKT_OSC_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SIZE.VALUE=16_MB
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_SVC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_125_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_117_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_109_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_98_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_31_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_23_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_15_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PINMUX_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PBIST_ALGO_3_4.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_LPO_BIAS_VALUE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_70_MAPPING.VALUE=70
DRIVER.SYSTEM.VAR.VIM_CHANNEL_62_MAPPING.VALUE=62
DRIVER.SYSTEM.VAR.VIM_CHANNEL_54_MAPPING.VALUE=54
DRIVER.SYSTEM.VAR.VIM_CHANNEL_46_MAPPING.VALUE=46
DRIVER.SYSTEM.VAR.VIM_CHANNEL_38_MAPPING.VALUE=38
DRIVER.SYSTEM.VAR.CLKT_AVCLK1_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL2_SOURCE_ENABLE.VALUE=0x00000080
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_PREFETCH_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_95_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_87_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_79_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HTU2_DP_PBISTCHECK_ENA.VALUE=0x00080000
DRIVER.SYSTEM.VAR.SAFETY_INIT_EMAC_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.PBIST_ALGO_15.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_111_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_103_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.PBIST_ALGO_16.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_VCLK2_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.RAM_LINK_LENGTH.VALUE=0x0003EB00
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_END_ADDRESS.VALUE=0x080017ff
DRIVER.SYSTEM.VAR.VIM_CHANNEL_30_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_22_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_14_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_7_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_LPO_OSCFRQCONFIGCNT_VALUE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK2_SOURCE.VALUE=PLL2
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_31_MAPPING.VALUE=31
DRIVER.SYSTEM.VAR.VIM_CHANNEL_23_MAPPING.VALUE=23
DRIVER.SYSTEM.VAR.VIM_CHANNEL_15_MAPPING.VALUE=15
DRIVER.SYSTEM.VAR.VIM_CHANNEL_11_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.PBIST_ALGO_5_6.VALUE=0
DRIVER.SYSTEM.VAR.PBIST_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_HCLK_DOMAIN_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_122_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_114_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_111_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_106_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_103_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_10_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.ETPWM5_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ETPWM_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL2_MUL.VALUE=120
DRIVER.SYSTEM.VAR.CLKT_RTI2_FREQ.VALUE=0.0
DRIVER.SYSTEM.VAR.CLKT_LPO_LOW_FREQ.VALUE=0.080
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_PREFETCH_ENTRY.VALUE=_prefetch
DRIVER.SYSTEM.VAR.VIM_CHANNEL_91_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_83_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_75_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_67_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_59_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_DMA_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_ENA.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK2_FREQ.VALUE=80.000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_PERMISSION_VALUE.VALUE=0x1300
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_PERMISSION_VALUE.VALUE=0x1300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_94_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_86_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_78_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_OSCILLATOR_SOURCE_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_BASE_ADDRESS.VALUE=0x60000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_124_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_116_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_108_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_60_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_52_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_44_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_36_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_28_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PMM_AUTO_CLK_WAKE_ENA.VALUE=0
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD4_STATE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HET2_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.PBIST_ALGO_7_8.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL2_RESET_ON_OSCILLATOR_FAIL.VALUE=0x00000000
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_BASE_ADDRESS_0.VALUE=0x00000020
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_BASE_ADDRESS_1.VALUE=0x00180000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SIZE_VALUE.VALUE=0x08
DRIVER.SYSTEM.VAR.VIM_CHANNEL_122_MAPPING.VALUE=122
DRIVER.SYSTEM.VAR.VIM_CHANNEL_114_MAPPING.VALUE=114
DRIVER.SYSTEM.VAR.VIM_CHANNEL_106_MAPPING.VALUE=106
DRIVER.SYSTEM.VAR.VIM_CHANNEL_6_NAME.VALUE=rtiOverflow0Interrupt
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD5_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.CORE_PMU_COUNTER2_EVENT.VALUE=0x11
DRIVER.SYSTEM.VAR.FLASH_DATA_WAIT_STATES.VALUE=3
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_95_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_87_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_79_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_PARITY_AVAILABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN3_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_RAMECC_SELFCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.ADC2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_VCLK2_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.FLASH_DATA_2_WAIT_STATE_FREQ.VALUE=135.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_5_MAPPING.VALUE=5
DRIVER.SYSTEM.VAR.VIM_PARITY_INTERRUPT_MAPPED_TO_VIM.VALUE=FALSE
DRIVER.SYSTEM.VAR.VIM_CHANNELS.VALUE=96
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_BASE_ADDRESS_7.VALUE=0xF0200000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SIZE.VALUE=512_BYTES
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_7_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.PMM_MEM_PD1_STATE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN3_DP_PBISTCHECK_ENA.VALUE=0x00000010
DRIVER.SYSTEM.VAR.ESM_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_99_MAPPING.VALUE=99
DRIVER.SYSTEM.VAR.VIM_CHANNEL_61_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_53_NAME.VALUE=mibspi5HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_45_NAME.VALUE=can3HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_37_NAME.VALUE=mibspi3HighInterruptLevel
DRIVER.SYSTEM.VAR.VIM_CHANNEL_29_NAME.VALUE=can1LowLevelInterrupt
DRIVER.SYSTEM.VAR.PMM_MEM_PD1_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_PERMISSION.VALUE=PRIV_RW_USER_RO_NOEXEC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_5_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SIZE.VALUE=2_KB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_127_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_119_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_60_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_52_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_44_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_36_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_28_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_6_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.EQEP1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_LPO_HIGH_FREQ.VALUE=10.000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SIZE_VALUE.VALUE=0x11
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_IRQ_ENTRY.VALUE="ldr pc,[pc,#-0x1b0]"
DRIVER.SYSTEM.VAR.VIM_CHANNEL_101_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_81_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_73_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_65_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_57_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_49_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ECAP_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SPI2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_GHV_POWER_DOWN_SOURCE.VALUE=OSC
DRIVER.SYSTEM.VAR.RAM_STACK_USER_BASE.VALUE=0x08000000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_120_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_112_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_104_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_92_MAPPING.VALUE=92
DRIVER.SYSTEM.VAR.VIM_CHANNEL_84_MAPPING.VALUE=84
DRIVER.SYSTEM.VAR.VIM_CHANNEL_76_MAPPING.VALUE=76
DRIVER.SYSTEM.VAR.VIM_CHANNEL_68_MAPPING.VALUE=68
DRIVER.SYSTEM.VAR.CLKT_GCLK_DOMAIN_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_1_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_81_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_73_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_65_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_57_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_49_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_ADC2_DP_PBISTCHECK_ENA.VALUE=0x00020000
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI4_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.SAFETY_INIT_USB_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.SYSTEM_INIT.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_20_NAME.VALUE=esmLowInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_12_NAME.VALUE=mibspi1HighLevelInterrupt
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_PERMISSION.VALUE=PRIV_NA_USER_NA_NOEXEC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_BASE_ADDRESS.VALUE=0x00000000
DRIVER.SYSTEM.VAR.VIM_CHANNEL_1_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_0_INT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_0_INT_TYPE.VALUE=FIQ
DRIVER.SYSTEM.VAR.DMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_PERMISSION_VALUE.VALUE=0x1100
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_PERMISSION_VALUE.VALUE=0x1200
DRIVER.SYSTEM.VAR.VIM_CHANNEL_61_MAPPING.VALUE=61
DRIVER.SYSTEM.VAR.VIM_CHANNEL_53_MAPPING.VALUE=53
DRIVER.SYSTEM.VAR.VIM_CHANNEL_45_MAPPING.VALUE=45
DRIVER.SYSTEM.VAR.VIM_CHANNEL_37_MAPPING.VALUE=37
DRIVER.SYSTEM.VAR.VIM_CHANNEL_29_MAPPING.VALUE=29
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_DIR.VALUE=1
DRIVER.SYSTEM.VAR.FLASH_LENGTH.VALUE=0x00300000
DRIVER.SYSTEM.VAR.RAM_STACK_FIQ_LENGTH.VALUE=0x00000100
DRIVER.SYSTEM.VAR.CLKT_EXT1_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_TYPE.VALUE=DEVICE_NONSHAREABLE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_100_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_51_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_43_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_35_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_27_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_21_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_19_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_13_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_ADC2_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_FREQ.VALUE=0.000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_TYPE_VALUE.VALUE=0x0010
DRIVER.SYSTEM.VAR.VIM_CHANNEL_95_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_87_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_79_NAME.VALUE=EMACRxIntISR
DRIVER.SYSTEM.VAR.CLKT_VCLK1_DIVIDER.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_PERMISSION.VALUE=PRIV_RW_USER_RW_NOEXEC
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_TYPE.VALUE=DEVICE_NONSHAREABLE
DRIVER.SYSTEM.VAR.VIM_CHANNEL_124_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_116_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_108_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_94_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_86_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_78_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.CAN2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_DOUT.VALUE=0
DRIVER.SYSTEM.VAR.PBIST_ALGO_1.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_DATA_0_WAIT_STATE_FREQ.VALUE=45.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_30_MAPPING.VALUE=30
DRIVER.SYSTEM.VAR.VIM_CHANNEL_22_MAPPING.VALUE=22
DRIVER.SYSTEM.VAR.VIM_CHANNEL_14_MAPPING.VALUE=14
DRIVER.SYSTEM.VAR.VIM_CHANNEL_7_NAME.VALUE=rtiOverflow1Interrupt
DRIVER.SYSTEM.VAR.PBIST_ALGO_2.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_RTI1_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CLKT_AVCLK1_DOMAIN_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_127_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_119_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_30_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_22_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_14_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI3_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.ETPWM3_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.DCC1_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.HET2_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_PBIST_ESRAM_SELECTED.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_VCLK3_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_FREQ1.VALUE=90.0
DRIVER.SYSTEM.VAR.PBIST_ALGO_11_12.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL2_SOURCE_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_END_ADDRESS.VALUE=0xfe0001ff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_94_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_86_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_78_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_21_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_13_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_HTU1_DP_PBISTCHECK_ENA.VALUE=0x00002000
DRIVER.SYSTEM.VAR.ECAP5_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ADC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_70_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_62_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_54_NAME.VALUE=spi4LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_46_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_38_NAME.VALUE=mibspi3LowLevelInterrupt
DRIVER.SYSTEM.VAR.FEE_FLASH_ECC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SIZE.VALUE=4_MB
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_RESET_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_110_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_102_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_6_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD4_STATE_AVAIL.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_121_MAPPING.VALUE=121
DRIVER.SYSTEM.VAR.VIM_CHANNEL_113_MAPPING.VALUE=113
DRIVER.SYSTEM.VAR.VIM_CHANNEL_105_MAPPING.VALUE=105
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_RESERVED_ENTRY.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_121_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_113_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_105_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_71_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_63_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_55_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_47_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_39_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.PMM_MEM_PD3_STATE_AVAIL.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_121_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_113_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_105_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_4_MAPPING.VALUE=4
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_DOMAIN_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_END_ADDRESS.VALUE=0x87ffffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SIZE.VALUE=4_GB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_120_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_112_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_104_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_PERMISSION_VALUE.VALUE=0x1300
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SIZE_VALUE.VALUE=0x17
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_PERMISSION_VALUE.VALUE=0x0300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_98_MAPPING.VALUE=98
DRIVER.SYSTEM.VAR.VIM_CHANNEL_21_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_13_NAME.VALUE=linHighLevelInterrupt
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD4_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_FUN.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_BASE_ADDRESS.VALUE=0x20000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_7_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_94_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_86_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_78_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_51_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_43_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_35_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_27_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_19_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_FRAY_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_DMA_DP_PBISTCHECK_ENA.VALUE=0x00000800
DRIVER.SYSTEM.VAR.HET_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.PBIST_ALGO_13_14.VALUE=0
DRIVER.SYSTEM.VAR.RAM_STACK_UNDEF_BASE.VALUE=0x08001400
DRIVER.SYSTEM.VAR.RAM_STACK_SVC_BASE.VALUE=0x08001000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_TYPE.VALUE=DEVICE_NONSHAREABLE
DRIVER.SYSTEM.VAR.VIM_CHANNEL_99_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_3_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.DMM_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.MIBSPI5_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_0.VALUE=ACTIVE
DRIVER.SYSTEM.VAR.PMM_PMCTRL_PWRDN.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK4_FREQ.VALUE=90.000
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_1.VALUE=ACTIVE
DRIVER.SYSTEM.VAR.VIM_CHANNEL_96_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_91_MAPPING.VALUE=91
DRIVER.SYSTEM.VAR.VIM_CHANNEL_88_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_83_MAPPING.VALUE=83
DRIVER.SYSTEM.VAR.VIM_CHANNEL_75_MAPPING.VALUE=75
DRIVER.SYSTEM.VAR.VIM_CHANNEL_67_MAPPING.VALUE=67
DRIVER.SYSTEM.VAR.VIM_CHANNEL_59_MAPPING.VALUE=59
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_2.VALUE=SLEEP
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_PERMISSION.VALUE=PRIV_RW_USER_RW_EXEC
DRIVER.SYSTEM.VAR.CLKT_VCLK2_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_3.VALUE=SLEEP
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_END_ADDRESS.VALUE=0x0803ffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_0_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_40_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_32_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_24_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_16_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_5_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD5_STATE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN2_DP_PBISTCHECK_ENA.VALUE=0x00000008
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_PDR.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_4.VALUE=SLEEP
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_5.VALUE=SLEEP
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SIZE_VALUE.VALUE=0x15
DRIVER.SYSTEM.VAR.VIM_CHANNEL_8_NAME.VALUE=rtiTimebaseInterrupt
DRIVER.SYSTEM.VAR.ECLK_PRESCALER.VALUE=8
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_6.VALUE=SLEEP
DRIVER.SYSTEM.VAR.FLASH_BANK_CONFIG_7.VALUE=ACTIVE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_BASE_ADDRESS.VALUE=0xFE000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_126_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_118_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_97_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_89_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_80_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_72_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_64_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_56_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_48_INT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HTU1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_VCLK4_FREQ.VALUE=0.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_60_MAPPING.VALUE=60
DRIVER.SYSTEM.VAR.VIM_CHANNEL_52_MAPPING.VALUE=52
DRIVER.SYSTEM.VAR.VIM_CHANNEL_44_MAPPING.VALUE=44
DRIVER.SYSTEM.VAR.VIM_CHANNEL_36_MAPPING.VALUE=36
DRIVER.SYSTEM.VAR.VIM_CHANNEL_28_MAPPING.VALUE=28
DRIVER.SYSTEM.VAR.CLKT_PLL1_BAND_WIDTH_ADJUSTMENT.VALUE=7
DRIVER.SYSTEM.VAR.CLKT_LPO_LOW_SOURCE_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.PMM_MEM_PD2_STATE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_VIM2_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CLKT_RTI1_POST_SOURCE.VALUE=VCLK
DRIVER.SYSTEM.VAR.VIM_CHANNEL_71_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_63_NAME.VALUE=het2HighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_55_NAME.VALUE=can3LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_47_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_39_NAME.VALUE=dmaHBCAInterrupt
DRIVER.SYSTEM.VAR.CLKT_PLL2_BAND_WIDTH_ADJUSTMENT.VALUE=7
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_DATA_ENTRY.VALUE=_dabort
DRIVER.SYSTEM.VAR.VIM_CHANNEL_80_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_72_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_64_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_56_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_48_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_ADC1_DP_PBISTCHECK_ENA.VALUE=0x00000400
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI3_DP_PBISTCHECK_ENA.VALUE=0x00000080
DRIVER.SYSTEM.VAR.VIM_CHANNEL_21_MAPPING.VALUE=21
DRIVER.SYSTEM.VAR.VIM_CHANNEL_13_MAPPING.VALUE=13
DRIVER.SYSTEM.VAR.CLKT_PLL2_REF_CLOCK_DIV.VALUE=4
DRIVER.SYSTEM.VAR.CLKT_PLL1_SPEADING_RATE.VALUE=255
DRIVER.SYSTEM.VAR.VIM_CHANNEL_50_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_42_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_34_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_26_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_18_INT_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_0_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN5_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.ETPWM1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL1_RESET_ON_SLIP.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_TYPE_VALUE.VALUE=0x0010
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_PERMISSION_VALUE.VALUE=0x0300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_127_MAPPING.VALUE=127
DRIVER.SYSTEM.VAR.VIM_CHANNEL_122_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_119_MAPPING.VALUE=119
DRIVER.SYSTEM.VAR.VIM_CHANNEL_114_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_106_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_PERMISSION.VALUE=PRIV_RW_USER_NA_NOEXEC
DRIVER.SYSTEM.VAR.CLKT_PLL1_FM_ENABLE.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SIZE.VALUE=4_MB
DRIVER.SYSTEM.VAR.VIM_CHANNEL_123_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_115_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_107_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_90_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_82_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_74_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_66_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_58_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_PBIST_ROM_PBIST_SELFCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.ECAP3_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_30_NAME.VALUE=spi2LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_22_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_14_NAME.VALUE=adc1Group0Interrupt
DRIVER.SYSTEM.VAR.CLKT_LPOLO_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_123_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_115_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_107_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_PSL.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_120_MAPPING.VALUE=120
DRIVER.SYSTEM.VAR.VIM_CHANNEL_112_MAPPING.VALUE=112
DRIVER.SYSTEM.VAR.VIM_CHANNEL_104_MAPPING.VALUE=104
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_END_ADDRESS.VALUE=0xffffffff
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SUB_4_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_BASE_ADDRESS.VALUE=0x80000000
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_SVC_ENTRY.VALUE=_svc
DRIVER.SYSTEM.VAR.VIM_CHANNEL_21_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_20_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_13_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_12_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CONFIG_NEW.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_FTU_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_FLASHECC_SELFCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_EXTERNAL2_FREQ.VALUE=00.0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_97_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_89_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_3_MAPPING.VALUE=3
DRIVER.SYSTEM.VAR.CLKT_LPOHI_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_101_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_99_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_93_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_85_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_77_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_69_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_6_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_RTP_DP_PBISTCHECK_ENA.VALUE=0x00004000
DRIVER.SYSTEM.VAR.CLKT_GHV_NORMAL_SOURCE.VALUE=PLL1
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_DIV_FREQ.VALUE=90.0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_97_MAPPING.VALUE=97
DRIVER.SYSTEM.VAR.VIM_CHANNEL_89_MAPPING.VALUE=89
DRIVER.SYSTEM.VAR.VIM_CHANNEL_9_NAME.VALUE=gioHighLevelInterrupt
DRIVER.SYSTEM.VAR.CLKT_PLL1_ENABLE.VALUE=TRUE
DRIVER.SYSTEM.VAR.FLASH_BASE_ADDRESS.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_11_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SUB_6_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_51_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_43_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_35_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_27_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_19_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_5_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_ESRAM_SP_PBISTCHECK_ENA.VALUE=0x08300020
DRIVER.SYSTEM.VAR.SAFETY_INIT_STC_ROM_PBIST_SELFCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.SPI5_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_AVCLK2_DOMAIN_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_TYPE.VALUE=NORMAL_OINC_NONSHARED
DRIVER.SYSTEM.VAR.VIM_CHANNEL_120_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_112_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_104_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_100_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_FTU_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.RTP_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.MIBSPI3_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_LENGTH_0.VALUE=0x0017FFE0
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_LENGTH_1.VALUE=0x00180000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_SIZE_VALUE.VALUE=0x16
DRIVER.SYSTEM.VAR.VIM_CHANNEL_90_MAPPING.VALUE=90
DRIVER.SYSTEM.VAR.VIM_CHANNEL_82_MAPPING.VALUE=82
DRIVER.SYSTEM.VAR.VIM_CHANNEL_80_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_74_MAPPING.VALUE=74
DRIVER.SYSTEM.VAR.VIM_CHANNEL_72_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_66_MAPPING.VALUE=66
DRIVER.SYSTEM.VAR.VIM_CHANNEL_64_NAME.VALUE=sciHighLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_58_MAPPING.VALUE=58
DRIVER.SYSTEM.VAR.VIM_CHANNEL_56_NAME.VALUE=mibspi5LowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_48_NAME.VALUE=ftuTransferInterrupt
DRIVER.SYSTEM.VAR.PMM_LOGIC_PD3_STATEVALUE.VALUE=0x5
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_7_DISABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_8_SUB_1_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_93_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_85_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_77_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_69_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.PBIST_ERRATA_4_CMS.VALUE=3
DRIVER.SYSTEM.VAR.EQEP_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.RTI_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.STC_MAX_TIMEOUT.VALUE=0xFFFFFFFF
DRIVER.SYSTEM.VAR.CLKT_LPO_LOW_TRIM.VALUE=100.00
DRIVER.SYSTEM.VAR.RAM_STACK_FIQ_BASE.VALUE=0x08001100
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_PERMISSION_VALUE.VALUE=0x0300
DRIVER.SYSTEM.VAR.VIM_CHANNEL_0_NAME.VALUE=esmHighInterrupt
DRIVER.SYSTEM.VAR.FLASH_BANK_LINK_LENGTH_7.VALUE=0x000010000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_SUB_2_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_50_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_42_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_34_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_26_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_18_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_HET1_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI5_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.FEE_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_10.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_LPO_LOW_TRIM_VALUE.VALUE=16
DRIVER.SYSTEM.VAR.VIM_CHANNEL_123_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_115_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_107_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_51_MAPPING.VALUE=51
DRIVER.SYSTEM.VAR.VIM_CHANNEL_43_MAPPING.VALUE=43
DRIVER.SYSTEM.VAR.VIM_CHANNEL_35_MAPPING.VALUE=35
DRIVER.SYSTEM.VAR.VIM_CHANNEL_27_MAPPING.VALUE=27
DRIVER.SYSTEM.VAR.VIM_CHANNEL_19_MAPPING.VALUE=19
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_11.VALUE=1
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_5_PERMISSION.VALUE=PRIV_RW_USER_RW_EXEC
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_12.VALUE=1
DRIVER.SYSTEM.VAR.CCM_MENU.VALUE=NONE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_9_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SIZE.VALUE=256_KB
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_BASE_ADDRESS.VALUE=0x08000000
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_2_SUB_3_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_98_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_20_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_12_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_4_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_PARITY_ENABLE_NEW.VALUE=0xA
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN2_RAMPARITYCHECK_ENA.VALUE=1
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_13.VALUE=1
DRIVER.SYSTEM.VAR.POM_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_14.VALUE=1
DRIVER.SYSTEM.VAR.CLKT_AVCLK3_SOURCE.VALUE=VCLK
DRIVER.SYSTEM.VAR.CLKT_PLL1_FREQ.VALUE=180.00
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_1_SIZE_VALUE.VALUE=0x1F
DRIVER.SYSTEM.VAR.VIM_CHANNEL_31_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_23_NAME.VALUE=gioLowLevelInterrupt
DRIVER.SYSTEM.VAR.VIM_CHANNEL_15_NAME.VALUE=adc1Group1Interrupt
DRIVER.SYSTEM.VAR.ERRATA_WORKAROUND_15.VALUE=1
DRIVER.SYSTEM.VAR.RAM_STACK_UNDEF_LENGTH.VALUE=0x00000100
DRIVER.SYSTEM.VAR.RAM_STACK_SVC_LENGTH.VALUE=0x00000100
DRIVER.SYSTEM.VAR.CLKT_LPO_TRIM_OTP_LOC.VALUE=0xF00801B4
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_6_SUB_6_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_HANDLER_TABLE_UNDEF_ENTRY.VALUE=_undef
DRIVER.SYSTEM.VAR.VIM_CHANNEL_93_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_85_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_77_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_71_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_69_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_63_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_55_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_47_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_39_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_CAN1_DP_PBISTCHECK_ENA.VALUE=0x00000004
DRIVER.SYSTEM.VAR.ETPWM6_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.DCC_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_20_MAPPING.VALUE=20
DRIVER.SYSTEM.VAR.VIM_CHANNEL_12_MAPPING.VALUE=12
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_7_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_ENDIAN_LITTLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_126_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_125_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_118_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.VIM_CHANNEL_117_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_109_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_FRAY_SP_PBISTCHECK_ENA.VALUE=0x00010000
DRIVER.SYSTEM.VAR.OS_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_12_SIZE_VALUE.VALUE=0x15
DRIVER.SYSTEM.VAR.VIM_CHANNEL_126_MAPPING.VALUE=126
DRIVER.SYSTEM.VAR.VIM_CHANNEL_118_MAPPING.VALUE=118
DRIVER.SYSTEM.VAR.VIM_CHANNEL_98_NAME.VALUE=phantomInterrupt
DRIVER.SYSTEM.VAR.ECLK_PORT_BIT0_PULL.VALUE=2
DRIVER.SYSTEM.VAR.ECLK_SUSPEND.VALUE=0
DRIVER.SYSTEM.VAR.CLKT_PLL1_SPEADING_AMOUNT.VALUE=61
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_10_SUB_5_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_4_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.CORE_VFP_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.SAFETY_INIT_VIM1_DP_PBISTCHECK_ENA.VALUE=0x00000200
DRIVER.SYSTEM.VAR.SAFETY_INIT_FMCBUS2_SELFCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.ECAP1_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.I2C_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.ECLK_OSCILLATOR_FREQ.VALUE=16.000
DRIVER.SYSTEM.VAR.CLKT_LPO_HIGH_TRIM.VALUE=100.00
DRIVER.SYSTEM.VAR.VIM_CHANNEL_9_MAPPING.VALUE=9
DRIVER.SYSTEM.VAR.CLKT_VCLK4_DOMAIN_ENABLE.VALUE=FALSE
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_3_SUB_0_DISABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_71_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_63_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_55_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_47_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_41_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_39_INT_PRAGMA_ENABLE.VALUE=1
DRIVER.SYSTEM.VAR.VIM_CHANNEL_33_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_25_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_17_INT_ENABLE.VALUE=0
DRIVER.SYSTEM.VAR.VIM_CHANNEL_9_INT_TYPE.VALUE=IRQ
DRIVER.SYSTEM.VAR.SAFETY_INIT_VIM2_RAMPARITYCHECK_ENA.VALUE=0
DRIVER.SYSTEM.VAR.SAFETY_INIT_MIBSPI2_DP_PBISTCHECK_ENA.VALUE=0x00000000
DRIVER.SYSTEM.VAR.CLKT_CRYSTAL_FREQ.VALUE=16.0
DRIVER.SYSTEM.VAR.CORE_MPU_REGION_7_TYPE_VALUE.VALUE=0x0008
DRIVER.SYSTEM.VAR.VIM_CHANNEL_111_MAPPING.VALUE=111
DRIVER.SYSTEM.VAR.VIM_CHANNEL_103_MAPPING.VALUE=103
DRIVER.SYSTEM.VAR.VIM_PHANTOM_NAME.VALUE=phantomInterrupt
DRIVER.OS.VAR.OS_USERECERSIVEMUTEXES.VALUE=0
DRIVER.OS.VAR.OS_USETIMERS.VALUE=0
DRIVER.OS.VAR.OS_USECNTSEMAPHORE.VALUE=0
DRIVER.OS.VAR.OS_GENERATERUNTIMESTATS.VALUE=0
DRIVER.OS.VAR.OS_USEMPU.VALUE=0
DRIVER.OS.VAR.OS_TOTALHEAPSIZE.VALUE=8192
DRIVER.OS.VAR.OS_USEVERBOSESTACK.VALUE=2
DRIVER.OS.VAR.OS_TIMERPRIORITY.VALUE=0
DRIVER.OS.VAR.OS_SVCENABLE.VALUE=0
DRIVER.OS.VAR.OS_MAXTASKNAMELEN.VALUE=16
DRIVER.OS.VAR.OS_MAXPRIORITIES.VALUE=5
DRIVER.OS.VAR.OS_TIMERTASKSTACKDEPTH.VALUE=0
DRIVER.OS.VAR.OS_COROUTINEPRIORITIES.VALUE=2
DRIVER.OS.VAR.OS_USECOROUTINES.VALUE=0
DRIVER.OS.VAR.OS_USEMUTEXES.VALUE=0
DRIVER.OS.VAR.OS_CPUCLOCKHZ.VALUE=80000000
DRIVER.OS.VAR.OS_USEMALLOCFAILEDHOOK.VALUE=0
DRIVER.OS.VAR.OS_MINSTACKSIZE.VALUE=128
DRIVER.OS.VAR.OS_SYSTEM_MODE.VALUE=0x1F
DRIVER.OS.VAR.OS_USEPREEMPTION.VALUE=1
DRIVER.OS.VAR.OS_IDLESHOULDYIELD.VALUE=1
DRIVER.OS.VAR.OS_USEIDLEHOOK.VALUE=0
DRIVER.OS.VAR.OS_TICKRATEHZ.VALUE=1000
DRIVER.OS.VAR.OS_TIMERPQUEUELENGTH.VALUE=0
DRIVER.OS.VAR.OS_USETRACE.VALUE=0
DRIVER.OS.VAR.OS_USESTACK.VALUE=0
DRIVER.OS.VAR.OS_USETICKHOOK.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL0_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL21_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL13_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL63_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL55_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL47_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL39_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL3_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL41_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL33_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL25_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL17_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL9_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL50_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL42_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL34_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL26_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL18_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL57_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL49_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL2_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL11_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL50_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL42_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL34_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL26_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL18_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL8_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL5_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL11_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL63_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL55_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL47_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL39_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL11_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL62_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL54_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL46_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL38_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL1_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL7_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL40_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL32_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL24_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL16_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL63_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL55_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL47_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL39_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL0_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL40_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL32_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL24_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL16_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL40_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL32_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL24_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL16_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL10_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL6_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL4_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL61_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL53_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL45_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL37_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL29_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL59_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL61_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL53_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL45_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL37_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL29_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL5_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL61_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL53_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL45_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL37_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL29_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL58_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_LOW_TIME.VALUE=182.044
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL30_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL22_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL14_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL31_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL23_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL15_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL9_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL30_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL22_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL14_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL4_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL3_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL51_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL43_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL35_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL27_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL19_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL58_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL58_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL3_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL9_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL51_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL43_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL35_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL27_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL19_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL60_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL52_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL44_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL36_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL28_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL20_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL12_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL56_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL48_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_VCLK_FREQ.VALUE=90
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL30_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL22_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL14_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL8_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL20_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL12_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL2_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL2_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL8_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL56_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL48_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL1_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL41_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL33_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL25_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL17_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL57_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL49_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL41_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL33_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL25_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL17_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL10_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL7_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL51_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL43_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL35_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL27_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL19_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL62_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL54_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL46_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL38_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL21_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL13_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL7_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL10_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL6_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL59_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL0_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL1_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL62_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL54_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL46_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL38_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL31_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL23_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL15_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL31_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL23_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL15_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL56_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL48_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL5_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL50_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL42_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL34_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL26_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL18_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL60_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL52_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL44_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL36_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL28_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL20_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL12_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL6_INT_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL59_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL4_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL60_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL52_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL44_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL36_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL28_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_LPC.VALUE=16384
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL21_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL13_ENABLE.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL57_INT_LEVEL.VALUE=0
DRIVER.ESM.VAR.ESM_GROUP0_CHANNEL49_INT_LEVEL.VALUE=0
DRIVER.RTI.VAR.RTI_1_COMPARE_2_SOURCE.VALUE=0x00000100
DRIVER.RTI.VAR.RTI_1_COMPARE_3_FREQ.VALUE=0.000100000
DRIVER.RTI.VAR.RTI_1_FREQ.VALUE=90.000
DRIVER.RTI.VAR.RTI_1_COMPARE_1_ACTUALTIME.VALUE=5.000
DRIVER.RTI.VAR.RTI_1_COUNTER_1_UC_COMPARE.VALUE=8
DRIVER.RTI.VAR.RTI_1_COMPARE_1_TIME.VALUE=5.000
DRIVER.RTI.VAR.RTI_1_COMPARE_3_UPDATE.VALUE=100000
DRIVER.RTI.VAR.RTI_1_CONTINUE_ON_SUSPEND_ENABLE.VALUE=0x00000000
DRIVER.RTI.VAR.RTI_1_COMPARE_1_INPUT_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COMPARE_0_SOURCE.VALUE=0x00000000
DRIVER.RTI.VAR.RTI_1_COMPARE_2_TIME.VALUE=8.000
DRIVER.RTI.VAR.RTI_1_COMPARE_0_ACTUALTIME.VALUE=1.000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_UC_COMPARE.VALUE=8
DRIVER.RTI.VAR.RTI_1_COMPARE_1_UPDATE.VALUE=50000
DRIVER.RTI.VAR.RTI_1_COMPARE_3_TIME.VALUE=10.000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_NTU_SOURCE.VALUE=0
DRIVER.RTI.VAR.RTI_1_COMPARE_0_INPUT_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_RTI_FREQ.VALUE=0.0
DRIVER.RTI.VAR.RTI_1_COUNTER_0_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COUNTER_1_FREQUENCY.VALUE=10.000
DRIVER.RTI.VAR.RTI_1_COMPARE_3_SOURCE.VALUE=0x00001000
DRIVER.RTI.VAR.RTI_1_COUNTER_1_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_NTU_SOURCE_REG.VALUE=0x0
DRIVER.RTI.VAR.RTI_1_COMPARE_1_SOURCE.VALUE=0x00000000
DRIVER.RTI.VAR.RTI_1_NTU_1_FREQ.VALUE=0.0
DRIVER.RTI.VAR.RTI_1_COMPARE_3_ACTUALTIME.VALUE=10.000
DRIVER.RTI.VAR.RTI_1_COMPARE_0_FREQ.VALUE=0.001000000
DRIVER.RTI.VAR.RTI_1_COMPARE_2_UPDATE.VALUE=80000
DRIVER.RTI.VAR.RTI_1_BASE.VALUE=0xFFFFFC00
DRIVER.RTI.VAR.RTI_1_NTU_2_FREQ.VALUE=0.0
DRIVER.RTI.VAR.RTI_1_COMPARE_3_INPUT_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COMPARE_1_FREQ.VALUE=0.000200000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_CAPTURE_SOURCE_ENABLE.VALUE=0
DRIVER.RTI.VAR.RTI_1_COUNTER_1_CAPTURE_SOURCE_ENABLE.VALUE=0
DRIVER.RTI.VAR.RTI_1_COMPARE_0_UPDATE.VALUE=10000
DRIVER.RTI.VAR.RTI_1_NTU_3_FREQ.VALUE=80.000
DRIVER.RTI.VAR.RTI_1_COMPARE_0.VALUE=10000
DRIVER.RTI.VAR.RTI_1_COMPARE_2_ACTUALTIME.VALUE=8.000
DRIVER.RTI.VAR.RTI_1_COMPARE_1.VALUE=50000
DRIVER.RTI.VAR.RTI_1_COMPARE_2.VALUE=80000
DRIVER.RTI.VAR.RTI_1_COMPARE_3.VALUE=100000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_NTU_FREQ.VALUE=0.000
DRIVER.RTI.VAR.RTI_1_COMPARE_2_FREQ.VALUE=0.000125000
DRIVER.RTI.VAR.RTI_1_COMPARE_0_TIME.VALUE=1.000
DRIVER.RTI.VAR.RTI_1_NTU_4_FREQ.VALUE=0.000
DRIVER.RTI.VAR.RTI_1_COMPARE_2_INPUT_FREQ.VALUE=10.000000000
DRIVER.RTI.VAR.RTI_1_COUNTER_0_FREQUENCY.VALUE=10.000
DRIVER.GIO.VAR.GIO_PORT1_BIT4_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT2_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT7_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT3_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT7_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT6_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT4_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT2_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT1_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT5_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT2_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT3_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT1_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT6_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT2_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT0_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT4_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT3_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT7_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT0_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT4_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_BASE_PORTA.VALUE=0xFFF7BC34
DRIVER.GIO.VAR.GIO_BASE_PORTB.VALUE=0xFFF7BC54
DRIVER.GIO.VAR.GIO_PORT1_BIT1_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT5_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT5_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT1_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT2_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_BASE.VALUE=0xFFF7BC00
DRIVER.GIO.VAR.GIO_PORT1_BIT3_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT6_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT7_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT2_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT3_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_DIR.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT4_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT6_PULDIS.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT4_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORTB_ENABLE.VALUE=1
DRIVER.GIO.VAR.GIO_PORT1_BIT5_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT3_PULL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT3_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_DOUT.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT7_PULL.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT0_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT5_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT4_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT0_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT4_DIR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT0_PULDIS.VALUE=1
DRIVER.GIO.VAR.GIO_PORT0_BIT1_DOUT.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT6_PSL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT2_PDR.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_LVL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT5_ENA.VALUE=0
DRIVER.GIO.VAR.GIO_PORT1_BIT7_POL.VALUE=0
DRIVER.GIO.VAR.GIO_PORT0_BIT1_PSL.VALUE=0
DRIVER.SCI.VAR.SCILIN_TIMMINGMODE.VALUE=1
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_DIR.VALUE=0
DRIVER.SCI.VAR.SCI_TIMMINGMODE.VALUE=1
DRIVER.SCI.VAR.SCILIN_WAKEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_PEINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT2_PULL.VALUE=2
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_DIR.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT0_DIR.VALUE=0
DRIVER.SCI.VAR.SCI_ACTUALBAUDRATE.VALUE=9599
DRIVER.SCI.VAR.SCI_EVENPARITY.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_FUN.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_DIR.VALUE=0
DRIVER.SCI.VAR.SCILIN_RXINTLVL.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT1_DIR.VALUE=0
DRIVER.SCI.VAR.SCI_BASE_PORT.VALUE=0xFFF7E540
DRIVER.SCI.VAR.SCILIN_PRESCALE.VALUE=585
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_PDR.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_FUN.VALUE=1
DRIVER.SCI.VAR.SCI_PORT_BIT0_FUN.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT2_DIR.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_PDR.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT0_PDR.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_FUN.VALUE=1
DRIVER.SCI.VAR.SCILIN_PEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT1_FUN.VALUE=1
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_PSL.VALUE=1
DRIVER.SCI.VAR.SCI_OEINTENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_PDR.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT1_PDR.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT2_FUN.VALUE=1
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_PSL.VALUE=1
DRIVER.SCI.VAR.SCI_PORT_BIT0_PSL.VALUE=1
DRIVER.SCI.VAR.SCI_PORT_BIT2_PDR.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_PSL.VALUE=1
DRIVER.SCI.VAR.SCI_PORT_BIT1_PSL.VALUE=1
DRIVER.SCI.VAR.SCILIN_BREAKINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_WAKEINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_BREAKINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT2_PSL.VALUE=1
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCI_FEINTENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_DOUT.VALUE=0
DRIVER.SCI.VAR.SCI_OEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCI_TXINTENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PARITYENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_DOUT.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT0_DOUT.VALUE=0
DRIVER.SCI.VAR.SCI_BAUDRATE.VALUE=9600
DRIVER.SCI.VAR.SCILIN_BREAKINTLVL.VALUE=0
DRIVER.SCI.VAR.SCI_WAKEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCI_BREAKINTLVL.VALUE=0
DRIVER.SCI.VAR.SCI_STOPBITS.VALUE=2
DRIVER.SCI.VAR.SCI_RXINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_FEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_EVENPARITY.VALUE=0
DRIVER.SCI.VAR.SCI_TXINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_OEINTENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_DOUT.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT1_DOUT.VALUE=0
DRIVER.SCI.VAR.SCI_PEINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_CLKMODE.VALUE=1
DRIVER.SCI.VAR.SCI_PARITYENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT0_PULL.VALUE=2
DRIVER.SCI.VAR.SCI_PORT_BIT2_DOUT.VALUE=0
DRIVER.SCI.VAR.SCILIN_BASE.VALUE=0xFFF7E400
DRIVER.SCI.VAR.SCI_RXINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_FEINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_PRESCALE.VALUE=585
DRIVER.SCI.VAR.SCILIN_OEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_TXINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_PULL.VALUE=2
DRIVER.SCI.VAR.SCI_PORT_BIT0_PULL.VALUE=2
DRIVER.SCI.VAR.SCI_PEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_WAKEINTENA.VALUE=0
DRIVER.SCI.VAR.SCI_LENGTH.VALUE=8
DRIVER.SCI.VAR.SCILIN_CLKMODE.VALUE=1
DRIVER.SCI.VAR.SCILIN_BASE_PORT.VALUE=0xFFF7E440
DRIVER.SCI.VAR.SCILIN_BAUDRATE.VALUE=9600
DRIVER.SCI.VAR.SCILIN_STOPBITS.VALUE=2
DRIVER.SCI.VAR.SCILIN_PORT_BIT2_PULL.VALUE=2
DRIVER.SCI.VAR.SCI_PORT_BIT1_PULL.VALUE=2
DRIVER.SCI.VAR.SCILIN_RXINTENA.VALUE=0
DRIVER.SCI.VAR.SCILIN_LENGTH.VALUE=8
DRIVER.SCI.VAR.SCILIN_FEINTLVL.VALUE=0
DRIVER.SCI.VAR.SCILIN_ACTUALBAUDRATE.VALUE=9599
DRIVER.SCI.VAR.SCILIN_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCI_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SCI.VAR.SCI_BASE.VALUE=0xFFF7E500
DRIVER.SCI.VAR.SCILIN_TXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_BITERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_CLKMOD.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PHASE0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_PHASE1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PHASE2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_PHASE3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_CSNR.VALUE=CS_2
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_C2TDELAYACTUAL.VALUE=22.222
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TIMEOUTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_LENGTH.VALUE=8
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_C2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_CSNR.VALUE=CS_1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_T2CDELAYACTUAL.VALUE=11.111
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PARERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_OVRNINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_CSNR.VALUE=CS_3
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_C2TDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_RXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_DEYSNCENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_BASE.VALUE=0xFFF7FC00
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_DEYSNCLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_CSNR.VALUE=CS_5
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_TXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PRESCALE0.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI1_CLKMOD.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_PRESCALE1.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI5_TXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PRESCALE2.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TIMEOUTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PRESCALE3.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARITYENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_PARITYENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_PARITYENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_DLENERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARITYENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_BITERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_T2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_BITERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_WDELAY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_T2CDELAYACTUAL.VALUE=11.111
DRIVER.MIBSPI.VAR.MIBSPI3_WDELAY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_WDELAY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_WDELAY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_ENABLEHIGHZ.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_CSNR.VALUE=CS_4
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_CSNR.VALUE=CS_6
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_BASE_PORT.VALUE=0xFFF7FC18
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_WAITENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_WAITENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_WAITENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_WAITENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PARPOL0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARPOL1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARPOL2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARPOL3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PARERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_OVRNINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_DEYSNCENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_RXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_T2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_RXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_DEYSNCLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_CSNR.VALUE=CS_1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_BAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_CSNR.VALUE=CS_7
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_BAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_BAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_BAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_DLENERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_C2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TIMEOUTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_BITERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_POLARITY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_POLARITY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_POLARITY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_BITERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_POLARITY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_CSNR.VALUE=CS_0
DRIVER.MIBSPI.VAR.MIBSPI5_T2CDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_LENGTH.VALUE=8
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_BASE_RAM.VALUE=0xFF0A0000
DRIVER.MIBSPI.VAR.MIBSPI5_CHARLEN0.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_CSDEF.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_CHARLEN1.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_CHARLEN2.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_CSNR.VALUE=CS_2
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_CHARLEN3.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_BASE_PORT.VALUE=0xFFF7F818
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_WAITENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_WAITENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_WAITENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_WAITENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_CSNR.VALUE=CS_4
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_PARERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_OVRNINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PARERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_DEYSNCLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_RXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_C2TDELAYACTUAL.VALUE=22.222
DRIVER.MIBSPI.VAR.MIBSPI1_WDELAY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_WDELAY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_WDELAY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_WDELAY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_BAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_CSNR.VALUE=CS_3
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_BAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_BAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_BAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_TIMEOUTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_ENABLEHIGHZ.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PARITYENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_C2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PARITYENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PARITYENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_DLENERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_PARITYENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_CSNR.VALUE=CS_5
DRIVER.MIBSPI.VAR.MIBSPI3_POLARITY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_C2TDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_POLARITY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_POLARITY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_BITERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_POLARITY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_C2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_CSNR.VALUE=CS_7
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_T2CDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_BASE_RAM.VALUE=0xFF0E0000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_PARPOL0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_CHARLEN0.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI1_PARPOL1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_CHARLEN1.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PARPOL2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_CHARLEN2.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_PARPOL3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_CHARLEN3.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_BASE_PORT.VALUE=0xFFF7F418
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_T2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_SHIFTDIR0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_SHIFTDIR1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_CSNR.VALUE=CS_0
DRIVER.MIBSPI.VAR.MIBSPI5_SHIFTDIR2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_SHIFTDIR3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_CSNR.VALUE=CS_6
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_MASTER.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_C2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PARERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_OVRNINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_DLENERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_T2CDELAYACTUAL.VALUE=11.111
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT9_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_BAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI1_BAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_BAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_BAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TIMEOUTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT11_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_CSNR.VALUE=CS_1
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_POLARITY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI1_POLARITY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_BASE.VALUE=0xFFF7F400
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_POLARITY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_RXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_POLARITY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PHASE0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PHASE1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PHASE2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT9_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PHASE3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_CSNR.VALUE=CS_3
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_LENGTH.VALUE=8
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT8_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT5_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_TXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_ENABLE.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_T2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT10_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT0_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_OVRNINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_T2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT1_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT11_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_SHIFTDIR0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_MASTER.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_SHIFTDIR1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_SHIFTDIR2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_SHIFTDIR3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_CSNR.VALUE=CS_2
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG4_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PRESCALE0.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI5_PRESCALE1.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PRESCALE2.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PRESCALE3.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI3_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.MIBSPI.VAR.MIBSPI5_PARITYENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_CSNR.VALUE=CS_4
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PARITYENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PARITYENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_DLENERRLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_CSDEF.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_PARITYENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_WAITENA0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_WAITENA1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_WAITENA2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG7_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_WAITENA3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_CSNR.VALUE=CS_6
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_T2EDELAYACTUAL.VALUE=0.000
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI1_TIMEOUTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG1_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_CLKMOD.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT9_DOUT.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PHASE0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_PHASE1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PHASE2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT11_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT1_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI3_PHASE3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT2_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG4_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG6_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_RXINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_WDELAY0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_ENABLEHIGHZ.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_WDELAY1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG5_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI5_WDELAY2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT17_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT25_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_WDELAY3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG5_BUF_CSNR.VALUE=CS_5
DRIVER.MIBSPI.VAR.MIBSPI3_BASE.VALUE=0xFFF7F800
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT27_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT19_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT3_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_MASTER.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT8_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG0_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG4_TRGSRC.VALUE=TRG_DISABLED
DRIVER.MIBSPI.VAR.MIBSPI3_C2EDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_TG2_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_LOCK.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT25_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT17_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_ONESHOT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_C2TDELAYACTUAL.VALUE=22.222
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT1_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG7_BUF_CSNR.VALUE=CS_7
DRIVER.MIBSPI.VAR.MIBSPI1_TG3_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_C2TDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_LENGTH.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_OVRNINTLVL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_SHIFTDIR0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT10_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG6_BUF_MODE.VALUE=4
DRIVER.MIBSPI.VAR.MIBSPI5_DEYSNCENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_SHIFTDIR1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG1_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT4_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_SHIFTDIR2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_SHIFTDIR3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_TG0_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_FUN.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT8_DIR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT0_PSL.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_T2CDELAY.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT4_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG2_TRGEVT.VALUE=TRG_ALWAYS
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT2_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI1_CSDEF.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_CS_ENCODE.VALUE=0xFF
DRIVER.MIBSPI.VAR.MIBSPI5_TG7_USE_CS_ENCODE.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TXINTENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT3_PULDIS.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG3_BUF_WDEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_TG2_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_TG6_PRST.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PRESCALE0.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI1_PORT_BIT5_DOUT.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI3_PRESCALE1.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI3_BASE_RAM.VALUE=0xFF0C0000
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT3_PULL.VALUE=2
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT0_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI5_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PARPOL0.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT10_DIR.VALUE=1
DRIVER.MIBSPI.VAR.MIBSPI3_TG3_BUF_CSHOLD.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PRESCALE2.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI3_CHARLEN0.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI5_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.MIBSPI.VAR.MIBSPI5_PARPOL1.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PRESCALE3.VALUE=89
DRIVER.MIBSPI.VAR.MIBSPI3_CHARLEN1.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI3_DLENERRENA.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT26_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PORT_BIT18_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI5_PARPOL2.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_PORT_BIT2_PDR.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG5_BUF_DFSEL.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_TG0_BUF_CSNR.VALUE=CS_0
DRIVER.MIBSPI.VAR.MIBSPI3_CHARLEN2.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI5_PARPOL3.VALUE=0
DRIVER.MIBSPI.VAR.MIBSPI3_CHARLEN3.VALUE=16
DRIVER.MIBSPI.VAR.MIBSPI1_TG1_TRGSRC.VALUE=TRG_DISABLED
DRIVER.SPI.VAR.SPI5_PORT_BIT26_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT18_DIR.VALUE=0
DRIVER.SPI.VAR.SPI3_PHASE2.VALUE=0
DRIVER.SPI.VAR.SPI2_TIMEOUTLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT1_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT25_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT17_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PHASE3.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT11_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_T2CDELAYACTUAL.VALUE=11.111
DRIVER.SPI.VAR.SPI4_POLARITY0.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT1_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT4_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI4_POLARITY1.VALUE=0
DRIVER.SPI.VAR.SPI2_T2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT2_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PORT_BIT9_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_POLARITY2.VALUE=0
DRIVER.SPI.VAR.SPI2_BITERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_SHIFTDIR0.VALUE=0
DRIVER.SPI.VAR.SPI1_RXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_DEYSNCENA.VALUE=0
DRIVER.SPI.VAR.SPI4_POLARITY3.VALUE=0
DRIVER.SPI.VAR.SPI1_SHIFTDIR1.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT10_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT2_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT4_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_SHIFTDIR2.VALUE=0
DRIVER.SPI.VAR.SPI1_SHIFTDIR3.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT2_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT10_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT8_DIR.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT0_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT0_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT25_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT17_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT11_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT4_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT11_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT25_DIR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT17_DIR.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT9_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_PRESCALE0.VALUE=89
DRIVER.SPI.VAR.SPI3_PRESCALE1.VALUE=89
DRIVER.SPI.VAR.SPI1_C2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT19_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT0_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PRESCALE2.VALUE=89
DRIVER.SPI.VAR.SPI1_MASTER.VALUE=1
DRIVER.SPI.VAR.SPI3_PRESCALE3.VALUE=89
DRIVER.SPI.VAR.SPI3_PORT_BIT2_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_C2TDELAY.VALUE=0
DRIVER.SPI.VAR.SPI2_BASE_PORT.VALUE=0xFFF7F618
DRIVER.SPI.VAR.SPI5_BITERRENA.VALUE=0
DRIVER.SPI.VAR.SPI4_OVRNINTENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT9_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT25_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT17_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_C2TDELAYACTUAL.VALUE=22.222
DRIVER.SPI.VAR.SPI4_PORT_BIT10_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT8_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_WAITENA0.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT11_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_WAITENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_OVRNINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_WAITENA2.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT9_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT9_DIR.VALUE=1
DRIVER.SPI.VAR.SPI4_WAITENA3.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT1_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_T2CDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT26_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT18_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT5_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_TXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT10_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_BASE_RAM.VALUE=0xFF0E0000
DRIVER.SPI.VAR.SPI5_PORT_BIT1_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT2_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_CHARLEN0.VALUE=16
DRIVER.SPI.VAR.SPI1_CHARLEN1.VALUE=16
DRIVER.SPI.VAR.SPI4_PORT_BIT8_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI1_CHARLEN2.VALUE=16
DRIVER.SPI.VAR.SPI1_CHARLEN3.VALUE=16
DRIVER.SPI.VAR.SPI5_PORT_BIT3_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT10_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT0_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_PARERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT8_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_DLENERRENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT26_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT18_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT9_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_PARITYENA0.VALUE=0
DRIVER.SPI.VAR.SPI4_ENABLEHIGHZ.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT3_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI5_T2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI4_PARITYENA1.VALUE=0
DRIVER.SPI.VAR.SPI4_PARITYENA2.VALUE=0
DRIVER.SPI.VAR.SPI4_DLENERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_RXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT4_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT25_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT17_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT11_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT3_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PARITYENA3.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT2_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT1_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI3_CLKMOD.VALUE=1
DRIVER.SPI.VAR.SPI1_PHASE0.VALUE=0
DRIVER.SPI.VAR.SPI1_PHASE1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT19_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT0_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PHASE2.VALUE=0
DRIVER.SPI.VAR.SPI1_PHASE3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT25_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT17_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT11_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_BAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT8_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_BAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PORT_BIT2_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_BAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_BAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_WDELAY0.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT4_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT10_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT25_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT17_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_C2TDELAYACTUAL.VALUE=22.222
DRIVER.SPI.VAR.SPI1_ENABLEHIGHZ.VALUE=0
DRIVER.SPI.VAR.SPI5_WDELAY1.VALUE=0
DRIVER.SPI.VAR.SPI4_C2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_WDELAY2.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT11_DIR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT9_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_TIMEOUTLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_WDELAY3.VALUE=0
DRIVER.SPI.VAR.SPI5_PARERRENA.VALUE=0
DRIVER.SPI.VAR.SPI4_RAM_PARITY_ENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT5_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT19_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT0_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT11_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_POLARITY0.VALUE=0
DRIVER.SPI.VAR.SPI2_POLARITY1.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT5_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_POLARITY2.VALUE=0
DRIVER.SPI.VAR.SPI3_DEYSNCENA.VALUE=0
DRIVER.SPI.VAR.SPI2_POLARITY3.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT11_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT1_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_DEYSNCLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT8_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PORT_BIT10_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT26_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT18_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_T2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI1_PORT_BIT9_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_T2CDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT3_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT10_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT0_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT3_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PORT_BIT9_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT5_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_C2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI1_PRESCALE0.VALUE=89
DRIVER.SPI.VAR.SPI4_BASE_RAM.VALUE=0xFF0E0000
DRIVER.SPI.VAR.SPI1_PRESCALE1.VALUE=89
DRIVER.SPI.VAR.SPI4_CHARLEN0.VALUE=16
DRIVER.SPI.VAR.SPI2_PORT_BIT1_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PRESCALE2.VALUE=89
DRIVER.SPI.VAR.SPI4_CHARLEN1.VALUE=16
DRIVER.SPI.VAR.SPI1_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_PRESCALE3.VALUE=89
DRIVER.SPI.VAR.SPI5_PORT_BIT1_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_T2CDELAYACTUAL.VALUE=11.111
DRIVER.SPI.VAR.SPI4_CHARLEN2.VALUE=16
DRIVER.SPI.VAR.SPI1_PORT_BIT8_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_CHARLEN3.VALUE=16
DRIVER.SPI.VAR.SPI1_BASE.VALUE=0xFFF7F400
DRIVER.SPI.VAR.SPI3_BITERRENA.VALUE=0
DRIVER.SPI.VAR.SPI3_OVRNINTENA.VALUE=0
DRIVER.SPI.VAR.SPI3_RXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI3_PORT_BIT10_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT4_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT2_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT25_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT17_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PORT_BIT2_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PARPOL0.VALUE=0
DRIVER.SPI.VAR.SPI5_BITERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_SHIFTDIR0.VALUE=0
DRIVER.SPI.VAR.SPI4_OVRNINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT8_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PARPOL1.VALUE=0
DRIVER.SPI.VAR.SPI4_SHIFTDIR1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT19_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT0_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_PARPOL2.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT0_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI4_SHIFTDIR2.VALUE=0
DRIVER.SPI.VAR.SPI5_PARPOL3.VALUE=0
DRIVER.SPI.VAR.SPI4_SHIFTDIR3.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT11_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT1_DIR.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT8_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT25_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT17_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT11_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_TXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT2_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT9_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_CLKMOD.VALUE=1
DRIVER.SPI.VAR.SPI5_TIMEOUTENA.VALUE=0
DRIVER.SPI.VAR.SPI2_DLENERRENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT11_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT5_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PARITYENA0.VALUE=0
DRIVER.SPI.VAR.SPI3_PARITYENA1.VALUE=0
DRIVER.SPI.VAR.SPI1_T2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT3_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_BASE_PORT.VALUE=0xFFF7FC18
DRIVER.SPI.VAR.SPI3_PORT_BIT9_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT9_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PARITYENA2.VALUE=0
DRIVER.SPI.VAR.SPI3_DLENERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT0_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_PARITYENA3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT1_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_WDELAY0.VALUE=0
DRIVER.SPI.VAR.SPI4_WDELAY1.VALUE=0
DRIVER.SPI.VAR.SPI4_WDELAY2.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT2_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT4_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_WDELAY3.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT10_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_T2CDELAYACTUAL.VALUE=11.111
DRIVER.SPI.VAR.SPI4_MASTER.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT2_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT8_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT3_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PORT_BIT0_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_BASE.VALUE=0xFFF7F600
DRIVER.SPI.VAR.SPI4_PORT_BIT10_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT8_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT0_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI3_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI3_PARERRENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI2_C2TDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PARERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT1_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT8_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_DEYSNCENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT2_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_WAITENA0.VALUE=0
DRIVER.SPI.VAR.SPI3_WAITENA1.VALUE=0
DRIVER.SPI.VAR.SPI3_WAITENA2.VALUE=0
DRIVER.SPI.VAR.SPI3_DEYSNCLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT3_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_WAITENA3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT26_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT18_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT11_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT8_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT10_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT10_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_TXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_BAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PORT_BIT1_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_C2TDELAYACTUAL.VALUE=22.222
DRIVER.SPI.VAR.SPI5_BAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_BAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_PORT_BIT11_DIR.VALUE=0
DRIVER.SPI.VAR.SPI4_PARPOL0.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT9_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_BAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_RAM_PARITY_ENA.VALUE=0
DRIVER.SPI.VAR.SPI4_PARPOL1.VALUE=0
DRIVER.SPI.VAR.SPI4_PARPOL2.VALUE=0
DRIVER.SPI.VAR.SPI4_PARPOL3.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT2_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT3_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_OVRNINTENA.VALUE=0
DRIVER.SPI.VAR.SPI1_BITERRENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT3_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_POLARITY0.VALUE=0
DRIVER.SPI.VAR.SPI4_PHASE0.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT0_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_POLARITY1.VALUE=0
DRIVER.SPI.VAR.SPI4_T2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI4_PHASE1.VALUE=0
DRIVER.SPI.VAR.SPI5_POLARITY2.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT8_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PHASE2.VALUE=0
DRIVER.SPI.VAR.SPI3_BITERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_OVRNINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_RXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_SHIFTDIR0.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT26_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT18_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_POLARITY3.VALUE=0
DRIVER.SPI.VAR.SPI4_PHASE3.VALUE=0
DRIVER.SPI.VAR.SPI2_SHIFTDIR1.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT9_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_T2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI2_SHIFTDIR2.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT8_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI1_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_SHIFTDIR3.VALUE=0
DRIVER.SPI.VAR.SPI1_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_PORT_BIT10_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT0_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT3_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PORT_BIT11_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PORT_BIT9_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI4_C2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI2_PORT_BIT2_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_BASE.VALUE=0xFFF7F800
DRIVER.SPI.VAR.SPI3_PORT_BIT1_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT8_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PRESCALE0.VALUE=89
DRIVER.SPI.VAR.SPI3_PORT_BIT8_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT4_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI3_WDELAY0.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT25_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT17_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI4_PRESCALE1.VALUE=89
DRIVER.SPI.VAR.SPI3_C2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI3_WDELAY1.VALUE=0
DRIVER.SPI.VAR.SPI4_PRESCALE2.VALUE=89
DRIVER.SPI.VAR.SPI3_WDELAY2.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT3_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PRESCALE3.VALUE=89
DRIVER.SPI.VAR.SPI4_TIMEOUTENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_WDELAY3.VALUE=0
DRIVER.SPI.VAR.SPI1_DLENERRENA.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT10_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_ENABLEHIGHZ.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT1_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_PARITYENA0.VALUE=0
DRIVER.SPI.VAR.SPI5_C2TDELAY.VALUE=0
DRIVER.SPI.VAR.SPI2_PARITYENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT8_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_TIMEOUTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_BASE_PORT.VALUE=0xFFF7F818
DRIVER.SPI.VAR.SPI2_PORT_BIT10_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_PARITYENA2.VALUE=0
DRIVER.SPI.VAR.SPI2_DLENERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_MASTER.VALUE=1
DRIVER.SPI.VAR.SPI2_PARITYENA3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT19_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT0_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT11_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT11_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_RAM_PARITY_ENA.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT3_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_T2CDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_TXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT3_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT9_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_BASE_RAM.VALUE=0xFF0C0000
DRIVER.SPI.VAR.SPI3_CHARLEN0.VALUE=16
DRIVER.SPI.VAR.SPI3_CHARLEN1.VALUE=16
DRIVER.SPI.VAR.SPI1_PARERRENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT11_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_CHARLEN2.VALUE=16
DRIVER.SPI.VAR.SPI2_PORT_BIT2_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_CHARLEN3.VALUE=16
DRIVER.SPI.VAR.SPI5_PORT_BIT9_DIR.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT9_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PORT_BIT0_FUN.VALUE=1
DRIVER.SPI.VAR.SPI3_PARERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_RXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT9_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_PARPOL0.VALUE=0
DRIVER.SPI.VAR.SPI1_DEYSNCLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_PARPOL1.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT8_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT10_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PARPOL2.VALUE=0
DRIVER.SPI.VAR.SPI2_T2CDELAYACTUAL.VALUE=11.111
DRIVER.SPI.VAR.SPI4_BASE.VALUE=0xFFF7FA00
DRIVER.SPI.VAR.SPI3_PARPOL3.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT2_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT0_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_CLKMOD.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT5_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI3_BAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PHASE0.VALUE=0
DRIVER.SPI.VAR.SPI3_BAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PHASE1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_DIR.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT8_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT0_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_BAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PHASE2.VALUE=0
DRIVER.SPI.VAR.SPI2_TXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_BAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PORT_BIT10_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_PHASE3.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT3_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT0_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT25_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT17_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_PORT_BIT11_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_OVRNINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT1_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI3_POLARITY0.VALUE=0
DRIVER.SPI.VAR.SPI3_POLARITY1.VALUE=0
DRIVER.SPI.VAR.SPI3_POLARITY2.VALUE=0
DRIVER.SPI.VAR.SPI2_OVRNINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT0_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_BITERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_DEYSNCENA.VALUE=0
DRIVER.SPI.VAR.SPI3_POLARITY3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT8_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT11_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_WDELAY0.VALUE=0
DRIVER.SPI.VAR.SPI2_WDELAY1.VALUE=0
DRIVER.SPI.VAR.SPI2_WDELAY2.VALUE=0
DRIVER.SPI.VAR.SPI2_WDELAY3.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT10_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_C2TDELAYACTUAL.VALUE=22.222
DRIVER.SPI.VAR.SPI5_PORT_BIT11_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT9_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT11_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT11_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_PRESCALE0.VALUE=89
DRIVER.SPI.VAR.SPI2_PRESCALE1.VALUE=89
DRIVER.SPI.VAR.SPI2_PORT_BIT8_DIR.VALUE=0
DRIVER.SPI.VAR.SPI2_PRESCALE2.VALUE=89
DRIVER.SPI.VAR.SPI3_TIMEOUTENA.VALUE=0
DRIVER.SPI.VAR.SPI2_PRESCALE3.VALUE=89
DRIVER.SPI.VAR.SPI1_PORT_BIT8_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT0_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PARITYENA0.VALUE=0
DRIVER.SPI.VAR.SPI1_C2TDELAY.VALUE=0
DRIVER.SPI.VAR.SPI1_PARITYENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT8_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_TIMEOUTLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT1_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_PARITYENA2.VALUE=0
DRIVER.SPI.VAR.SPI1_DLENERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_BASE_PORT.VALUE=0xFFF7F418
DRIVER.SPI.VAR.SPI5_RXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI4_BITERRENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT1_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_PARITYENA3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT9_PDR.VALUE=0
DRIVER.SPI.VAR.SPI4_T2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI2_PORT_BIT8_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_WAITENA0.VALUE=0
DRIVER.SPI.VAR.SPI5_BASE.VALUE=0xFFF7FC00
DRIVER.SPI.VAR.SPI2_WAITENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_SHIFTDIR0.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_WAITENA2.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT10_DIR.VALUE=1
DRIVER.SPI.VAR.SPI5_SHIFTDIR1.VALUE=0
DRIVER.SPI.VAR.SPI2_WAITENA3.VALUE=0
DRIVER.SPI.VAR.SPI5_C2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI5_SHIFTDIR2.VALUE=0
DRIVER.SPI.VAR.SPI5_SHIFTDIR3.VALUE=0
DRIVER.SPI.VAR.SPI1_TXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT8_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT1_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT26_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT18_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_TXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT9_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_PARPOL0.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT0_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT9_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_PARPOL1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT2_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI2_PARPOL2.VALUE=0
DRIVER.SPI.VAR.SPI2_PARPOL3.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT2_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_PARERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_CLKMOD.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT10_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_T2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT11_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_RXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT11_DIR.VALUE=0
DRIVER.SPI.VAR.SPI2_RAM_PARITY_ENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT11_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT0_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT8_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT9_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_BAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_BAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT8_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT1_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_BAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI3_PORT_BIT4_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT25_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT17_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_BAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI5_PORT_BIT11_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_WDELAY0.VALUE=0
DRIVER.SPI.VAR.SPI2_C2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI1_WDELAY1.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT9_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_MASTER.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT10_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT3_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_WDELAY2.VALUE=0
DRIVER.SPI.VAR.SPI4_PARERRENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT10_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_WDELAY3.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT9_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT8_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_C2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI1_POLARITY0.VALUE=0
DRIVER.SPI.VAR.SPI4_C2TDELAY.VALUE=0
DRIVER.SPI.VAR.SPI1_POLARITY1.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT4_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT25_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT17_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_POLARITY2.VALUE=0
DRIVER.SPI.VAR.SPI1_OVRNINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_DLENERRENA.VALUE=0
DRIVER.SPI.VAR.SPI2_DEYSNCENA.VALUE=0
DRIVER.SPI.VAR.SPI1_POLARITY3.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_WAITENA0.VALUE=0
DRIVER.SPI.VAR.SPI5_ENABLEHIGHZ.VALUE=0
DRIVER.SPI.VAR.SPI3_T2CDELAYACTUAL.VALUE=11.111
DRIVER.SPI.VAR.SPI1_PORT_BIT1_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_WAITENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_WAITENA2.VALUE=0
DRIVER.SPI.VAR.SPI4_DEYSNCLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT9_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_WAITENA3.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT9_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT10_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT2_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI4_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_T2CDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT27_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT19_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_PORT_BIT0_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT2_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_TXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT3_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT0_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT0_PSL.VALUE=1
DRIVER.SPI.VAR.SPI2_BASE_RAM.VALUE=0xFF0E0000
DRIVER.SPI.VAR.SPI2_CHARLEN0.VALUE=16
DRIVER.SPI.VAR.SPI1_PORT_BIT11_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT4_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT11_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_CHARLEN1.VALUE=16
DRIVER.SPI.VAR.SPI2_TIMEOUTENA.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT9_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_CHARLEN2.VALUE=16
DRIVER.SPI.VAR.SPI2_ENABLEHIGHZ.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT11_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_CHARLEN3.VALUE=16
DRIVER.SPI.VAR.SPI3_PORT_BIT0_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_TIMEOUTLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_BITERRENA.VALUE=0
DRIVER.SPI.VAR.SPI1_RXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT11_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT2_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT10_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_RXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_BITERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_SHIFTDIR0.VALUE=0
DRIVER.SPI.VAR.SPI1_PARPOL0.VALUE=0
DRIVER.SPI.VAR.SPI3_SHIFTDIR1.VALUE=0
DRIVER.SPI.VAR.SPI1_PARPOL1.VALUE=0
DRIVER.SPI.VAR.SPI5_PHASE0.VALUE=0
DRIVER.SPI.VAR.SPI4_C2TDELAYACTUAL.VALUE=22.222
DRIVER.SPI.VAR.SPI3_SHIFTDIR2.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT8_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT11_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_PARPOL2.VALUE=0
DRIVER.SPI.VAR.SPI5_PHASE1.VALUE=0
DRIVER.SPI.VAR.SPI3_SHIFTDIR3.VALUE=0
DRIVER.SPI.VAR.SPI1_PARPOL3.VALUE=0
DRIVER.SPI.VAR.SPI5_PHASE2.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT9_PULL.VALUE=2
DRIVER.SPI.VAR.SPI1_PORT_BIT3_FUN.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT27_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT19_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_PHASE3.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT1_PSL.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT8_DIR.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_DIR.VALUE=1
DRIVER.SPI.VAR.SPI1_TXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI5_PRESCALE0.VALUE=89
DRIVER.SPI.VAR.SPI1_PORT_BIT10_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_C2EDELAY.VALUE=0
DRIVER.SPI.VAR.SPI5_PRESCALE1.VALUE=89
DRIVER.SPI.VAR.SPI5_PRESCALE2.VALUE=89
DRIVER.SPI.VAR.SPI3_PORT_BIT5_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT1_DIR.VALUE=1
DRIVER.SPI.VAR.SPI5_PRESCALE3.VALUE=89
DRIVER.SPI.VAR.SPI3_PORT_BIT9_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_T2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI3_PORT_BIT8_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI2_ACTUALBAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT3_PDR.VALUE=0
DRIVER.SPI.VAR.SPI2_ACTUALBAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_BASE_PORT.VALUE=0xFFF7FA18
DRIVER.SPI.VAR.SPI2_PORT_BIT10_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_ACTUALBAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_PORT_BIT0_PULL.VALUE=2
DRIVER.SPI.VAR.SPI5_OVRNINTENA.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI2_ACTUALBAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PORT_BIT9_PSL.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT3_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI5_PORT_BIT1_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_MASTER.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT4_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT1_DOUT.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT2_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_T2CDELAY.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT9_DIR.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT0_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_PORT_BIT11_PSL.VALUE=1
DRIVER.SPI.VAR.SPI5_BASE_RAM.VALUE=0xFF0A0000
DRIVER.SPI.VAR.SPI5_CHARLEN0.VALUE=16
DRIVER.SPI.VAR.SPI3_PORT_BIT10_PULL.VALUE=2
DRIVER.SPI.VAR.SPI3_PORT_BIT2_DIR.VALUE=1
DRIVER.SPI.VAR.SPI5_CHARLEN1.VALUE=16
DRIVER.SPI.VAR.SPI2_PARERRENA.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT4_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI5_CHARLEN2.VALUE=16
DRIVER.SPI.VAR.SPI1_PORT_BIT4_PDR.VALUE=0
DRIVER.SPI.VAR.SPI5_CHARLEN3.VALUE=16
DRIVER.SPI.VAR.SPI5_PORT_BIT25_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT17_DIR.VALUE=0
DRIVER.SPI.VAR.SPI5_PORT_BIT11_PULL.VALUE=2
DRIVER.SPI.VAR.SPI4_PARERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI2_PORT_BIT11_DIR.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT0_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_DLENERRENA.VALUE=0
DRIVER.SPI.VAR.SPI4_RXINTENA.VALUE=0
DRIVER.SPI.VAR.SPI3_RAM_PARITY_ENA.VALUE=0
DRIVER.SPI.VAR.SPI5_PARITYENA0.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT0_PDR.VALUE=0
DRIVER.SPI.VAR.SPI1_WAITENA0.VALUE=0
DRIVER.SPI.VAR.SPI5_PARITYENA1.VALUE=0
DRIVER.SPI.VAR.SPI1_WAITENA1.VALUE=0
DRIVER.SPI.VAR.SPI5_PARITYENA2.VALUE=0
DRIVER.SPI.VAR.SPI5_DLENERRLVL.VALUE=0
DRIVER.SPI.VAR.SPI4_PORT_BIT8_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_DEYSNCLVL.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT5_FUN.VALUE=1
DRIVER.SPI.VAR.SPI1_WAITENA2.VALUE=0
DRIVER.SPI.VAR.SPI5_PARITYENA3.VALUE=0
DRIVER.SPI.VAR.SPI1_WAITENA3.VALUE=0
DRIVER.SPI.VAR.SPI1_PORT_BIT3_PSL.VALUE=1
DRIVER.SPI.VAR.SPI3_PORT_BIT1_FUN.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT8_PULL.VALUE=2
DRIVER.SPI.VAR.SPI2_PORT_BIT9_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI4_BAUDRATE0.VALUE=1000.000
DRIVER.SPI.VAR.SPI1_T2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI4_BAUDRATE1.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_BAUDRATE2.VALUE=1000.000
DRIVER.SPI.VAR.SPI4_TXINTLVL.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT3_DIR.VALUE=1
DRIVER.SPI.VAR.SPI2_PORT_BIT10_FUN.VALUE=1
DRIVER.SPI.VAR.SPI4_BAUDRATE3.VALUE=1000.000
DRIVER.SPI.VAR.SPI2_PORT_BIT2_PULDIS.VALUE=0
DRIVER.SPI.VAR.SPI1_TIMEOUTENA.VALUE=0
DRIVER.SPI.VAR.SPI5_CLKMOD.VALUE=1
DRIVER.SPI.VAR.SPI4_PORT_BIT8_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_PORT_BIT9_DOUT.VALUE=0
DRIVER.SPI.VAR.SPI3_PHASE0.VALUE=0
DRIVER.SPI.VAR.SPI2_C2EDELAYACTUAL.VALUE=0.000
DRIVER.SPI.VAR.SPI1_PORT_BIT5_PDR.VALUE=0
DRIVER.SPI.VAR.SPI3_PHASE1.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_SYNC.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_BAUDRATE.VALUE=500
DRIVER.CAN.VAR.CAN_2_PORT_RX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_ID.VALUE=30
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_ID.VALUE=22
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_ID.VALUE=14
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_ID.VALUE=9
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_RAMBASE.VALUE=0xFF1C0000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_NOMINAL_BIT_RATE.VALUE=500.000
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_PIN_MODE.VALUE=1
DRIVER.CAN.VAR.CAN_2_PHASE_SEG.VALUE=3
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_ID.VALUE=31
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_ID.VALUE=23
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_ID.VALUE=15
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_AUTO_BUS_ON.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_BASE.VALUE=0xFFF7DC00
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_ID.VALUE=40
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_ID.VALUE=32
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_ID.VALUE=24
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_ID.VALUE=16
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_RX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_ID.VALUE=41
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_ID.VALUE=33
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_ID.VALUE=25
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_ID.VALUE=17
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_BRP_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_DIR.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PROP_SEG.VALUE=5
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_ID.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_NOMINAL_BIT_RATE.VALUE=500.000
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_ID.VALUE=50
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_ID.VALUE=42
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_ID.VALUE=34
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_ID.VALUE=26
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_ID.VALUE=18
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PROPAGATION_DELAY.VALUE=700
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_ID.VALUE=2
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_NOMINAL_BIT_TIME.VALUE=12
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_SAMPLE_POINT.VALUE=75.000
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_ID.VALUE=51
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_ID.VALUE=43
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_ID.VALUE=35
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_ID.VALUE=27
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_ID.VALUE=19
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_ID.VALUE=3
DRIVER.CAN.VAR.CAN_2_PORT_RX_DOUT.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_BASE.VALUE=0xFFF7DE00
DRIVER.CAN.VAR.CAN_1_RAMBASE.VALUE=0xFF1E0000
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_RX_PULL.VALUE=2
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_IDENTIFIER_MODE.VALUE=0x40000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_ID.VALUE=60
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_ID.VALUE=52
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_ID.VALUE=44
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_ID.VALUE=36
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_ID.VALUE=28
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_ID.VALUE=4
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_AUTO_RETRANSMISSION.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_ID.VALUE=61
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_ID.VALUE=53
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_ID.VALUE=45
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_ID.VALUE=37
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_ID.VALUE=29
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_TX_PULL.VALUE=2
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_NOMINAL_BIT_RATE.VALUE=500.000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_ID.VALUE=5
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_BRPE_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_RX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_ID.VALUE=62
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_ID.VALUE=54
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_ID.VALUE=46
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_ID.VALUE=38
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_NOMINAL_BIT_TIME.VALUE=12
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_PORT_RX_DIR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_TQ.VALUE=166.667
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_ID.VALUE=6
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_ID.VALUE=63
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_ID.VALUE=55
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_ID.VALUE=47
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_ID.VALUE=39
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_TQ.VALUE=166.667
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_BRPE.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_ID.VALUE=7
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_BASE.VALUE=0xFFF7E000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_PORT_TX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_ID.VALUE=64
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_ID.VALUE=56
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_ID.VALUE=48
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_DOUT.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_TQ.VALUE=166.667
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_ID.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_ID.VALUE=10
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_ID.VALUE=57
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_ID.VALUE=49
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_SAMPLE_POINT_REFERENCE.VALUE=75
DRIVER.CAN.VAR.CAN_1_PROPAGATION_DELAY.VALUE=700
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_ID.VALUE=9
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_ID.VALUE=11
DRIVER.CAN.VAR.CAN_1_NOMINAL_BIT_TIME.VALUE=12
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_RX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_ENABLE.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PIN_MODE.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_ID.VALUE=58
DRIVER.CAN.VAR.CAN_2_SAMPLE_POINT_REFERENCE.VALUE=75
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_RX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_ID.VALUE=20
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_ID.VALUE=12
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_AUTO_BUS_ON.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_ID.VALUE=59
DRIVER.CAN.VAR.CAN_1_PORT_RX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_SAMPLE_POINT_REFERENCE.VALUE=75
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_SHIFT.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_BRPE.VALUE=0
DRIVER.CAN.VAR.CAN_1_MASK.VALUE=0x1FFFFFFF
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_ID.VALUE=21
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_ID.VALUE=13
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_BRPE_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_1_BRP_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_ID.VALUE=30
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_ID.VALUE=22
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_ID.VALUE=14
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_BAUDRATE.VALUE=500
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_ID.VALUE=31
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_ID.VALUE=23
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_ID.VALUE=15
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_RX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_SAMPLE_POINT.VALUE=75.000
DRIVER.CAN.VAR.CAN_1_PORT_TX_DIR.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.CAN.VAR.CAN_3_PHASE_SEG.VALUE=3
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_ID.VALUE=40
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_ID.VALUE=32
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_ID.VALUE=24
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_ID.VALUE=16
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_PORT_RX_DOUT.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_RX_PULL.VALUE=2
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_BRPE.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MASK.VALUE=0x1FFFFFFF
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_ID.VALUE=41
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_ID.VALUE=33
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_ID.VALUE=25
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_ID.VALUE=17
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_ID.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_PORT_TX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_ID.VALUE=50
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_ID.VALUE=42
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_ID.VALUE=34
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_ID.VALUE=26
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_ID.VALUE=18
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_BRP.VALUE=14
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_PROP_SEG.VALUE=5
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_ID.VALUE=10
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_ID.VALUE=2
DRIVER.CAN.VAR.CAN_1_AUTO_BUS_ON_TR.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_RX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_ID.VALUE=51
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_ID.VALUE=43
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_ID.VALUE=35
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_ID.VALUE=27
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_ID.VALUE=19
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_AUTO_BUS_ON_TIME.VALUE=0
DRIVER.CAN.VAR.CAN_3_PORT_RX_DIR.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_ID.VALUE=11
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_ID.VALUE=3
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_ID.VALUE=60
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_ID.VALUE=52
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_ID.VALUE=44
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_ID.VALUE=36
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_ID.VALUE=28
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_BRP.VALUE=14
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_PORT_RX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_ID.VALUE=20
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_ID.VALUE=12
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_ID.VALUE=4
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_NOMINAL_AUTO_BUS_ON_TIME.VALUE=0.000
DRIVER.CAN.VAR.CAN_2_SHIFT.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MASK.VALUE=0x1FFFFFFF
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_TX_DOUT.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_31_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_23_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_15_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_ID.VALUE=61
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_ID.VALUE=53
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_ID.VALUE=45
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_ID.VALUE=37
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_ID.VALUE=29
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_PULL.VALUE=2
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_ID.VALUE=21
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_ID.VALUE=13
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_ID.VALUE=5
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_ID.VALUE=62
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_ID.VALUE=54
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_ID.VALUE=46
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_ID.VALUE=38
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_BRP.VALUE=14
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_IDENTIFIER_MODE.VALUE=0x40000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PHASE_SEG.VALUE=3
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_ID.VALUE=30
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_ID.VALUE=22
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_ID.VALUE=14
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_ID.VALUE=6
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_AUTO_BUS_ON_TIME.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_RX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_ID.VALUE=63
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_ID.VALUE=55
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_ID.VALUE=47
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_ID.VALUE=39
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_ID.VALUE=31
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_ID.VALUE=23
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_20_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_ID.VALUE=15
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_12_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_PORT_TX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_ID.VALUE=7
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_PORT_RX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_ID.VALUE=64
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_ID.VALUE=56
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_ID.VALUE=48
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_ID.VALUE=40
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_ID.VALUE=32
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_ID.VALUE=24
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_ID.VALUE=16
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_ID.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_NOMINAL_AUTO_BUS_ON_TIME.VALUE=0.000
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_AUTO_RETRANSMISSION.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_ID.VALUE=57
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_ID.VALUE=49
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_AUTO_BUS_ON.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_40_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_32_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_24_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_16_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_ID.VALUE=41
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_ID.VALUE=33
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_ID.VALUE=25
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_ID.VALUE=17
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_ID.VALUE=9
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_SJW.VALUE=3
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_BAUDRATE.VALUE=500
DRIVER.CAN.VAR.CAN_1_MESSAGE_61_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_53_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_45_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_37_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_29_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_IDENTIFIER_MODE.VALUE=0x40000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_ID.VALUE=58
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_AUTO_BUS_ON_TIME.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_ID.VALUE=50
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_ID.VALUE=42
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_ID.VALUE=34
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_ID.VALUE=26
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_ID.VALUE=18
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_TX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_PIN_MODE.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_30_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_22_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_14_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_PORT_RX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_AUTO_BUS_ON_TR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_ID.VALUE=59
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_TX_DIR.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_4_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_ID.VALUE=51
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_ID.VALUE=43
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_ID.VALUE=35
DRIVER.CAN.VAR.CAN_3_MESSAGE_30_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_ID.VALUE=27
DRIVER.CAN.VAR.CAN_3_MESSAGE_22_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_ID.VALUE=19
DRIVER.CAN.VAR.CAN_3_MESSAGE_14_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_6_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_SJW.VALUE=3
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_RX_PULL.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_50_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_42_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_34_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_26_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_18_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_ID.VALUE=60
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_ID.VALUE=52
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_ID.VALUE=44
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_ID.VALUE=36
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_ID.VALUE=28
DRIVER.CAN.VAR.CAN_1_MESSAGE_63_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_55_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_47_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_39_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_NOMINAL_AUTO_BUS_ON_TIME.VALUE=0.000
DRIVER.CAN.VAR.CAN_1_SYNC.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_11_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_SHIFT.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_PORT_RX_PULDIS.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_40_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_32_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_24_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_16_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_ID.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_BRP_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_2_BRPE_FREQ.VALUE=6.000
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PROP_SEG.VALUE=5
DRIVER.CAN.VAR.CAN_1_MESSAGE_41_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_33_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_25_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_17_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_ID.VALUE=61
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_ID.VALUE=53
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_ID.VALUE=45
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_ID.VALUE=37
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_ID.VALUE=29
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_SJW.VALUE=3
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_1_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_3_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_ID.VALUE=2
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_8_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_10_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_SAMPLE_POINT.VALUE=75.000
DRIVER.CAN.VAR.CAN_2_MESSAGE_51_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_43_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_35_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_27_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_19_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_ID.VALUE=62
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_ID.VALUE=54
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_ID.VALUE=46
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_ID.VALUE=38
DRIVER.CAN.VAR.CAN_1_MESSAGE_64_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_56_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_48_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_PORT_TX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_2_MESSAGE_1_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_21_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_13_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_7_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_5_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_60_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_52_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_44_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_36_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_28_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_ID.VALUE=3
DRIVER.CAN.VAR.CAN_1_PORT_RX_DOUT.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_1_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_ID.VALUE=63
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_ID.VALUE=55
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_ID.VALUE=47
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_ID.VALUE=39
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_RAMBASE.VALUE=0xFF1A0000
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_PORT_RX_DIN.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_59_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_PORT_TX_PDR.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_ID.VALUE=4
DRIVER.CAN.VAR.CAN_1_MESSAGE_31_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_23_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_15_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_PORT_RX_DIR.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_PORT_TX_DOUT.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_64_ID.VALUE=64
DRIVER.CAN.VAR.CAN_3_MESSAGE_56_ID.VALUE=56
DRIVER.CAN.VAR.CAN_3_MESSAGE_48_ID.VALUE=48
DRIVER.CAN.VAR.CAN_3_MESSAGE_31_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_23_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_15_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_57_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_49_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_3_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_SYNC.VALUE=1
DRIVER.CAN.VAR.CAN_2_PORT_TX_PULL.VALUE=2
DRIVER.CAN.VAR.CAN_2_MESSAGE_2_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_64_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_63_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_56_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_55_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_48_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_47_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_39_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_61_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_53_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_45_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_37_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_29_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_ID.VALUE=10
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_ID.VALUE=5
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_57_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_49_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_4_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_50_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_42_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_34_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_26_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_18_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_ID.VALUE=57
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_ID.VALUE=49
DRIVER.CAN.VAR.CAN_3_MESSAGE_41_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_33_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_25_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_17_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_9_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_62_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_54_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_46_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_38_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_51_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_43_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_35_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_27_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_19_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_9_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_41_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_33_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_25_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_17_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_11_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_5_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_58_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_11_ID.VALUE=11
DRIVER.CAN.VAR.CAN_2_MESSAGE_6_ID.VALUE=6
DRIVER.CAN.VAR.CAN_3_PROPAGATION_DELAY.VALUE=700
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_57_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_49_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_AUTO_RETRANSMISSION.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_20_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_12_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_9_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_58_ID.VALUE=58
DRIVER.CAN.VAR.CAN_1_MESSAGE_60_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_52_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_44_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_36_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_28_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_3_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_7_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_10_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_PORT_TX_PSL.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_60_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_52_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_44_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_36_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_28_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_62_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_54_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_46_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_38_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_20_ID.VALUE=20
DRIVER.CAN.VAR.CAN_2_MESSAGE_12_ID.VALUE=12
DRIVER.CAN.VAR.CAN_2_MESSAGE_7_ID.VALUE=7
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_2_MESSAGE_59_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_51_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_43_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_35_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_27_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_19_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_61_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_53_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_45_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_40_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_37_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_32_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_29_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_24_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_16_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_3_MESSAGE_10_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_1_MESSAGE_8_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_2_MASK.VALUE=0x000007FF
DRIVER.CAN.VAR.CAN_3_MESSAGE_59_ID.VALUE=59
DRIVER.CAN.VAR.CAN_3_MESSAGE_50_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_42_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_34_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_26_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_18_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_AUTO_BUS_ON_TR.VALUE=0
DRIVER.CAN.VAR.CAN_2_MESSAGE_30_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_22_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_14_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_5_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_4_DLC.VALUE=8
DRIVER.CAN.VAR.CAN_3_MESSAGE_63_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_62_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_55_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_54_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_47_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_46_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_39_DIR.VALUE=0x20000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_38_INT_ENA_REF.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_PORT_RX_FUN.VALUE=1
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_BOOL_ENA.VALUE=0
DRIVER.CAN.VAR.CAN_1_MESSAGE_58_INT_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_21_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_1_MESSAGE_13_ENA.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_6_RTR.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_3_MESSAGE_2_INT_LEVEL.VALUE=0x00000000
DRIVER.CAN.VAR.CAN_2_MESSAGE_21_ID.VALUE=21
DRIVER.CAN.VAR.CAN_2_MESSAGE_13_ID.VALUE=13
DRIVER.CAN.VAR.CAN_2_MESSAGE_8_ID.VALUE=8
DRIVER.ADC.VAR.ADC2_GROUP1_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_PIN21_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_GROUP1_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC2_GROUP0_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_PARITY_ENABLE.VALUE=0x00000005
DRIVER.ADC.VAR.ADC1_GROUP2_PIN17_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC1_GROUP2_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC2_GROUP2_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP2_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC1_GROUP1_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP2_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_LENGTH.VALUE=16
DRIVER.ADC.VAR.ADC2_GROUP1_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC2_PORT_BIT0_DIR.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_ALT_TRIG.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC2_GROUP2_LENGTH.VALUE=32
DRIVER.ADC.VAR.ADC2_GROUP2_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN22_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP2_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP2_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN18_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_BND.VALUE=2
DRIVER.ADC.VAR.ADC2_GROUP0_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN23_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_PORT_BIT0_PULDIS.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP2_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP0_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC2_GROUP0_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC1_GROUP1_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC2_GROUP1_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC2_BND.VALUE=2
DRIVER.ADC.VAR.ADC2_GROUP1_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_PORT_BIT0_PDR.VALUE=0
DRIVER.ADC.VAR.ADC2_ACTUAL_CYCLE_TIME.VALUE=100.00
DRIVER.ADC.VAR.ADC2_GROUP1_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_TRIGGER_MODE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN19_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.ADC.VAR.ADC2_GROUP1_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC2_GROUP0_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC1_GROUP2_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP0_LENGTH.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP0_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC1_PORT_BIT0_PULDIS.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP2_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN20_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN24_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN16_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP0_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_TRIGGER_MODE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_PORT_BIT0_PSL.VALUE=1
DRIVER.ADC.VAR.ADC1_GROUP2_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC1_GROUP0_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_LENGTH.VALUE=64
DRIVER.ADC.VAR.ADC2_GROUP0_DISCHARGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN21_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PINS.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP0_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_GROUP0_SAMPLE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP0_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP2_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_RAMBASE.VALUE=0xFF3A0000
DRIVER.ADC.VAR.ADC2_GROUP0_BND.VALUE=8
DRIVER.ADC.VAR.ADC1_PORT_BIT0_DOUT.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC1_GROUP0_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC2_GROUP2_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC2_GROUP1_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP1_BND.VALUE=8
DRIVER.ADC.VAR.ADC2_GROUP0_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP1_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_ALT_TRIG_COMP.VALUE=1
DRIVER.ADC.VAR.ADC2_GROUP1_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP1_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN17_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_PARITY_ENABLE.VALUE=0x00000005
DRIVER.ADC.VAR.ADC1_ACTUAL_CYCLE_TIME.VALUE=100.00
DRIVER.ADC.VAR.ADC2_GROUP2_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP2_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_PINS.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_PIN22_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN18_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP1_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_RAMBASE.VALUE=0xFF3E0000
DRIVER.ADC.VAR.ADC1_BASE.VALUE=0xFFF7C000
DRIVER.ADC.VAR.ADC1_PORT_BIT0_DIR.VALUE=0
DRIVER.ADC.VAR.ADC2_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.ADC.VAR.ADC2_GROUP2_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP2_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP2_LENGTH.VALUE=32
DRIVER.ADC.VAR.ADC1_GROUP0_BND.VALUE=8
DRIVER.ADC.VAR.ADC2_GROUP2_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC2_GROUP2_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PINS.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP0_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP1_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC1_GROUP0_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_GROUP2_TRIGGER_MODE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP2_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN23_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_BND.VALUE=8
DRIVER.ADC.VAR.ADC2_GROUP2_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN19_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_PORT_BIT0_DOUT.VALUE=0
DRIVER.ADC.VAR.ADC2_CYCLE_TIME.VALUE=100.00
DRIVER.ADC.VAR.ADC1_GROUP1_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC1_GROUP1_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP0_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_PRESCALE.VALUE=8
DRIVER.ADC.VAR.ADC1_GROUP1_PIN20_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP0_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_BASE.VALUE=0xFFF7C200
DRIVER.ADC.VAR.ADC2_GROUP0_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP2_PIN24_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN16_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_PORT_BIT0_PDR.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP1_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC1_GROUP0_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC1_PORT_BIT0_PULL.VALUE=2
DRIVER.ADC.VAR.ADC1_GROUP0_LENGTH.VALUE=16
DRIVER.ADC.VAR.ADC2_GROUP1_CONVERSION_TIME.VALUE=1.300
DRIVER.ADC.VAR.ADC1_GROUP0_PINS.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP0_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC2_GROUP1_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC1_GROUP1_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC1_GROUP0_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP0_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP1_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_PORT_BIT0_PSL.VALUE=1
DRIVER.ADC.VAR.ADC1_GROUP2_EXTENDED_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_LENGTH.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP1_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC1_GROUP2_PIN3_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN21_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC1_GROUP2_HW_TRIGGER_SOURCE_ALT.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP2_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN17_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_GROUP0_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC2_GROUP0_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN22_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_CYCLE_TIME.VALUE=100.00
DRIVER.ADC.VAR.ADC2_GROUP0_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP2_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN7_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC1_GROUP0_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC1_GROUP1_PIN0_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_ID_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_DISCHARGE_TIME.VALUE=0.00
DRIVER.ADC.VAR.ADC1_GROUP2_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC1_GROUP1_PINS.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_ALT_TRIG_COMP.VALUE=1
DRIVER.ADC.VAR.ADC1_GROUP0_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_ALT_TRIG.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP1_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_RAM_PARITY_ENA.VALUE=0
DRIVER.ADC.VAR.ADC1_GROUP0_PIN4_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_DISCHARGE_PRESCALER.VALUE=0
DRIVER.ADC.VAR.ADC2_GROUP1_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN8_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN18_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_RESOLUTION.VALUE=12_BIT
DRIVER.ADC.VAR.ADC1_GROUP2_PIN1_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN11_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN23_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN15_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_HW_TRIGGER_SOURCE.VALUE=EVENT
DRIVER.ADC.VAR.ADC2_GROUP0_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN19_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_PRESCALE.VALUE=8
DRIVER.ADC.VAR.ADC2_GROUP0_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP2_PINS.VALUE=0
DRIVER.ADC.VAR.ADC2_PORT_BIT0_PULL.VALUE=2
DRIVER.ADC.VAR.ADC1_LENGTH.VALUE=64
DRIVER.ADC.VAR.ADC2_GROUP2_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN20_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN12_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_CHANNEL_TOTAL_TIME.VALUE=0.000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_CONTINUOUS_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_PIN5_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_ACTUAL_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC1_GROUP0_SCAN_TIME.VALUE=0.000
DRIVER.ADC.VAR.ADC2_GROUP1_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_PIN13_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP0_TRIGGER_EDGE_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN9_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_SAMPLE_TIME.VALUE=200.00
DRIVER.ADC.VAR.ADC2_GROUP1_FIFO_SIZE.VALUE=16
DRIVER.ADC.VAR.ADC1_GROUP0_PIN2_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP1_PIN10_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP2_PIN6_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN24_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_PIN16_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC2_GROUP2_PIN14_ENABLE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP1_TRIGGER_MODE.VALUE=0x00000000
DRIVER.ADC.VAR.ADC1_GROUP0_ACTUAL_DISCHARGE_TIME.VALUE=0.00
DRIVER.LIN.VAR.LIN_PORT_BIT0_DOUT.VALUE=0
DRIVER.LIN.VAR.LIN_PEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TOAWUSINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_BEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TOA3WUSINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT1_DOUT.VALUE=0
DRIVER.LIN.VAR.LIN_MAXPRESCALE.VALUE=4046
DRIVER.LIN.VAR.LIN_LENGTH.VALUE=8
DRIVER.LIN.VAR.LIN_PARITYENA.VALUE=0
DRIVER.LIN.VAR.LIN_BREAKINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TX_MASK.VALUE=0xFF
DRIVER.LIN.VAR.LIN_MSTMOD.VALUE=1
DRIVER.LIN.VAR.LIN_SDEL.VALUE=1
DRIVER.LIN.VAR.LIN_PORT_BIT2_DOUT.VALUE=0
DRIVER.LIN.VAR.LIN_TOAWUSINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_WAKEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_HGENCTRL.VALUE=1
DRIVER.LIN.VAR.LIN_TOA3WUSINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT0_DIR.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT0_PULL.VALUE=2
DRIVER.LIN.VAR.LIN_CEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_BREAKINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PBEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT1_DIR.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT0_FUN.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT1_PULDIS.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT2_DIR.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT1_PULL.VALUE=2
DRIVER.LIN.VAR.LIN_WAKEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT0_PDR.VALUE=0
DRIVER.LIN.VAR.LIN_OEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT1_FUN.VALUE=2
DRIVER.LIN.VAR.LIN_NREINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT1_PDR.VALUE=0
DRIVER.LIN.VAR.LIN_PORT_BIT2_FUN.VALUE=4
DRIVER.LIN.VAR.LIN_CEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT0_PSL.VALUE=1
DRIVER.LIN.VAR.LIN_PORT_BIT2_PULL.VALUE=2
DRIVER.LIN.VAR.LIN_PBEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT2_PDR.VALUE=0
DRIVER.LIN.VAR.LIN_BASE_PORT.VALUE=0xFFF7E440
DRIVER.LIN.VAR.LIN_ACTUALBAUDRATE.VALUE=20.018
DRIVER.LIN.VAR.LIN_PORT_BIT1_PSL.VALUE=2
DRIVER.LIN.VAR.LIN_ISFEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_FEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT2_PSL.VALUE=4
DRIVER.LIN.VAR.LIN_OEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TXINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_NREINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_IDINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_SBREAK.VALUE=13
DRIVER.LIN.VAR.LIN_TOINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_BAUDRATE.VALUE=20.000
DRIVER.LIN.VAR.LIN_RX_MASK.VALUE=0xFF
DRIVER.LIN.VAR.LIN_ISFEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_RXINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_BASE.VALUE=0xFFF7E400
DRIVER.LIN.VAR.LIN_FEINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TXINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PORT_BIT2_PULDIS.VALUE=0
DRIVER.LIN.VAR.LIN_IDINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_TOINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_MAXBAUDRATE.VALUE=22.244
DRIVER.LIN.VAR.LIN_BEINTENA.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_RXINTLVL.VALUE=0x00000000
DRIVER.LIN.VAR.LIN_PRESCALE.VALUE=280
DRIVER.LIN.VAR.LIN_PORT_BIT0_PULDIS.VALUE=0
DRIVER.HET.VAR.HET2_EDGE5_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_PWM0_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT0_PULL.VALUE=1
DRIVER.HET.VAR.HET2_INT_X0.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE4_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_BIT1_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_HRSHARE.VALUE=0x00000008
DRIVER.HET.VAR.HET2_INT_X1.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_BIT29_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT0_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_IGNORE_SUSPEND_ENABLE.VALUE=0x00020000
DRIVER.HET.VAR.HET2_PWM3_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_PWM1_PIN_SELECT.VALUE=10
DRIVER.HET.VAR.HET2_BIT12_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT3_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_INT_X2.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X3.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_INT_X4.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_PWM0_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT4_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM3_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT4_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X5.VALUE=0x00000000
DRIVER.HET.VAR.HET2_IGNORE_SUSPEND_ENABLE.VALUE=0x00020000
DRIVER.HET.VAR.HET1_BIT30_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT26_HRSHARE.VALUE=0x00002000
DRIVER.HET.VAR.HET1_BIT22_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM4_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT3_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X6.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE0_PIN_SELECT.VALUE=9
DRIVER.HET.VAR.HET1_BIT28_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT7_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_INT_X7.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT7_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X8.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT18_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT10_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X9.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT11_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT11_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_PWM4_PIN_SELECT.VALUE=16
DRIVER.HET.VAR.HET2_PWM4_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET1_RAM_BASE.VALUE=0xFF460000
DRIVER.HET.VAR.HET2_EDGE6_BOTH.VALUE=0
DRIVER.HET.VAR.HET2_PWM2_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT15_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE2_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_BIT11_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP3_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT24_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT5_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT27_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT19_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE6_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT24_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT16_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT2_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE3_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_EDGE5_PIN_SELECT.VALUE=21
DRIVER.HET.VAR.HET2_BIT13_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT27_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT19_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_CAP5_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_PWM4_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT8_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_CAP2_PIN_SELECT.VALUE=4
DRIVER.HET.VAR.HET2_BIT4_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT29_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT0_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT8_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_BIT1_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT12_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_PWM4_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_BIT18_PULL.VALUE=1
DRIVER.HET.VAR.HET2_BIT16_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_BIT0_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT6_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP5_PIN_SELECT.VALUE=26
DRIVER.HET.VAR.HET1_BIT28_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT10_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_EDGE7_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM0_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT1_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE7_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_EDGE5_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_PWM5_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_BIT3_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE7_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_EDGE1_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_PWM1_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT14_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT4_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_EDGE4_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_BIT5_PULL.VALUE=1
DRIVER.HET.VAR.HET1_MASTER.VALUE=1
DRIVER.HET.VAR.HET2_EDGE5_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_ENA.VALUE=0
DRIVER.HET.VAR.HET2_PWM0_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT2_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT5_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_BIT8_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT1_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_LR_ACTUALTIME.VALUE=1422.222
DRIVER.HET.VAR.HET1_PWM5_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_PWM5_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM4_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_BIT9_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM0_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT5_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_PIN_SELECT.VALUE=18
DRIVER.HET.VAR.HET2_BIT13_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT12_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_EDGE7_BOTH.VALUE=0
DRIVER.HET.VAR.HET2_BIT17_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_ENA.VALUE=1
DRIVER.HET.VAR.HET1_BIT30_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT27_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT19_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_BIT7_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE5_PIN_SELECT.VALUE=21
DRIVER.HET.VAR.HET1_BIT29_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT0_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET_DIS_BLACKBOX.VALUE=0
DRIVER.HET.VAR.HET2_PWM7_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_HR_ACTUALFREQUENCY.VALUE=90.000
DRIVER.HET.VAR.HET2_PWM5_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_BIT25_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT17_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT4_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE0_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_CAP6_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT20_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT15_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X10.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT28_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_INT_X11.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X20.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X12.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_PWM6_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT16_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X21.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X13.VALUE=0x00000000
DRIVER.HET.VAR.HET2_HR_PRESCALE.VALUE=0
DRIVER.HET.VAR.HET1_EDGE6_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM6_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_LR_PRESCALE.VALUE=7
DRIVER.HET.VAR.HET2_PWM0_PIN_SELECT.VALUE=8
DRIVER.HET.VAR.HET2_BIT6_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X30.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X22.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X14.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X31.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X23.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X15.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_INT_X24.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X16.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BASE_PORT.VALUE=0xFFF7B84C
DRIVER.HET.VAR.HET2_PWM5_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT0_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X25.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X17.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_PWM3_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT10_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT14_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X26.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X18.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X27.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X19.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT18_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X28.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_PWM0_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_BIT18_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_INT_X29.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_ENA.VALUE=0
DRIVER.HET.VAR.HET2_CAP7_PIN_SELECT.VALUE=6
DRIVER.HET.VAR.HET2_BIT8_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_PWM4_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_PWM3_PIN_SELECT.VALUE=14
DRIVER.HET.VAR.HET1_BIT11_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT1_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM0_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_BIT2_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE6_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_BIT5_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT14_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_BIT20_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT16_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT5_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM0_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_BIT6_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM7_ENA.VALUE=0
DRIVER.HET.VAR.HET1_PWM0_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT8_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE4_PIN_SELECT.VALUE=20
DRIVER.HET.VAR.HET2_BIT7_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT9_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT3_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT10_PULL.VALUE=1
DRIVER.HET.VAR.HET2_CAP1_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_EDGE6_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP1_PIN_SELECT.VALUE=2
DRIVER.HET.VAR.HET2_BIT15_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT13_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_EDGE3_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_PWM6_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT9_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE0_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_PWM2_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_BIT26_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT18_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT6_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE3_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT16_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT17_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP4_PIN_SELECT.VALUE=24
DRIVER.HET.VAR.HET1_BIT29_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT0_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM2_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_EDGE3_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT3_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_HRSHARE.VALUE=0x00000002
DRIVER.HET.VAR.HET2_PWM6_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_BIT8_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_BIT4_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT4_HRSHARE.VALUE=0x00000004
DRIVER.HET.VAR.HET1_BIT25_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT17_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT16_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_PWM4_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_PWM1_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_PWM7_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_PWM3_ENA.VALUE=0
DRIVER.HET.VAR.HET1_BIT24_HRSHARE.VALUE=0x00001000
DRIVER.HET.VAR.HET1_BIT16_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT10_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_PIN_SELECT.VALUE=17
DRIVER.HET.VAR.HET1_BIT20_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT12_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT3_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X10.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X11.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT3_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE7_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_EDGE0_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT7_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X20.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X12.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT14_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_BIT10_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X21.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X13.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT18_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_EDGE4_PIN_SELECT.VALUE=20
DRIVER.HET.VAR.HET1_INT_X30.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X22.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X14.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET1_INT_X31.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X23.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X15.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE5_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_PWM1_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_BIT7_PULL.VALUE=1
DRIVER.HET.VAR.HET1_INT_X24.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X16.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP2_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM3_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X25.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X17.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT9_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT5_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X26.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X18.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X27.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X19.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT11_PULL.VALUE=1
DRIVER.HET.VAR.HET1_INT_X28.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_RAM_SIZE.VALUE=160
DRIVER.HET.VAR.HET1_EDGE2_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_INT_X29.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT17_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT14_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_PWM3_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_PWM5_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_PWM1_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_BIT10_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP4_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_BIT8_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT4_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_ENA.VALUE=0
DRIVER.HET.VAR.HET1_PWM0_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_BIT4_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_EDGE1_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM1_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT27_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT19_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT8_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT14_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP6_PIN_SELECT.VALUE=4
DRIVER.HET.VAR.HET1_PWM2_PIN_SELECT.VALUE=12
DRIVER.HET.VAR.HET1_BIT1_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM3_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_PWM2_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_EDGE4_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT28_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE0_BOTH.VALUE=0
DRIVER.HET.VAR.HET2_EDGE6_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_PWM5_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT8_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT4_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE3_PIN_SELECT.VALUE=15
DRIVER.HET.VAR.HET2_PWM2_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT18_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT11_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT10_PULL.VALUE=1
DRIVER.HET.VAR.HET2_EDGE1_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_PWM5_ENA.VALUE=0
DRIVER.HET.VAR.HET1_BIT8_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP0_PIN_SELECT.VALUE=0
DRIVER.HET.VAR.HET1_BIT21_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT13_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT5_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_BIT4_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE2_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_BIT9_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_BIT1_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM4_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_BIT7_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_PWM7_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_BIT8_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM2_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT12_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT31_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT23_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT15_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP3_PIN_SELECT.VALUE=6
DRIVER.HET.VAR.HET1_BIT7_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_PWM4_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_BIT12_PULL.VALUE=1
DRIVER.HET.VAR.HET1_PWM0_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_PWM0_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_CAP5_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT26_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT15_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM0_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_EDGE4_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_BIT20_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT12_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE5_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM6_ENA.VALUE=0
DRIVER.HET.VAR.HET1_BIT6_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE1_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_PWM3_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE3_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT28_PULL.VALUE=1
DRIVER.HET.VAR.HET2_CAP7_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM3_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_PWM4_PIN_SELECT.VALUE=16
DRIVER.HET.VAR.HET1_BIT10_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_BIT9_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE3_PIN_SELECT.VALUE=15
DRIVER.HET.VAR.HET1_PWM1_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT8_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BASE.VALUE=0xFFF7B800
DRIVER.HET.VAR.HET2_EDGE1_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_PWM6_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT12_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT2_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_PIN_SELECT.VALUE=19
DRIVER.HET.VAR.HET1_PWM5_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_LR_ACTUALTIME.VALUE=1422.222
DRIVER.HET.VAR.HET1_BIT21_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT13_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT11_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM3_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_PWM7_ENA.VALUE=0
DRIVER.HET.VAR.HET1_HR_PRESCALE.VALUE=0
DRIVER.HET.VAR.HET1_BIT30_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT22_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT14_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT7_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_HR_ACTUALFREQUENCY.VALUE=90.000
DRIVER.HET.VAR.HET2_PWM1_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT5_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE4_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP0_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_BIT4_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE2_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_PWM2_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT8_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT11_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_EDGE6_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_MASTER.VALUE=1
DRIVER.HET.VAR.HET1_PWM5_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT9_PULL.VALUE=1
DRIVER.HET.VAR.HET2_EDGE6_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT10_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP5_PIN_SELECT.VALUE=2
DRIVER.HET.VAR.HET1_PWM1_PIN_SELECT.VALUE=10
DRIVER.HET.VAR.HET1_BIT9_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT13_PULL.VALUE=1
DRIVER.HET.VAR.HET1_PWM7_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_PWM5_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT24_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.HET.VAR.HET2_BIT16_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT10_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE0_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_BIT30_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X0.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE2_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_PWM4_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT28_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT0_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT0_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X1.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE2_PIN_SELECT.VALUE=13
DRIVER.HET.VAR.HET2_PWM2_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT8_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X2.VALUE=0x00000000
DRIVER.HET.VAR.HET1_DIS_BLACKBOX.VALUE=0
DRIVER.HET.VAR.HET2_LR_TIME.VALUE=800.000
DRIVER.HET.VAR.HET1_INT_X3.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_EDGE5_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_BIT29_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT0_PULL.VALUE=1
DRIVER.HET.VAR.HET1_INT_X4.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT2_HRSHARE.VALUE=0x00000002
DRIVER.HET.VAR.HET1_BIT21_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT13_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X5.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_BIT20_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT3_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_INT_X6.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET1_INT_X7.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BASE_PORT.VALUE=0xFFF7B94C
DRIVER.HET.VAR.HET1_INT_X8.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT17_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT2_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_BIT30_HRSHARE.VALUE=0x00008000
DRIVER.HET.VAR.HET1_BIT22_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_INT_X9.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BASE.VALUE=0xFFF7B900
DRIVER.HET.VAR.HET2_EDGE2_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_EDGE0_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_BIT10_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP2_PIN_SELECT.VALUE=4
DRIVER.HET.VAR.HET1_BIT11_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_PWM0_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT31_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT23_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT20_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT15_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM4_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_BIT18_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP1_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT30_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_PWM6_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT31_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT23_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT15_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT9_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE6_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT7_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE1_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_RAM_SIZE.VALUE=160
DRIVER.HET.VAR.HET2_BIT9_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT21_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT13_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT0_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_CAP3_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_BIT0_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_PIN_SELECT.VALUE=14
DRIVER.HET.VAR.HET2_PWM6_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT14_PULL.VALUE=1
DRIVER.HET.VAR.HET1_EDGE7_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT17_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_EDGE2_PIN_SELECT.VALUE=13
DRIVER.HET.VAR.HET1_BIT20_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_HR_FREQUENCY.VALUE=90.000
DRIVER.HET.VAR.HET2_PWM2_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_EDGE5_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_EDGE1_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_PWM5_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT24_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT18_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE0_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT0_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_PWM6_PIN_SELECT.VALUE=18
DRIVER.HET.VAR.HET2_EDGE2_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_EDGE7_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT1_PULL.VALUE=1
DRIVER.HET.VAR.HET2_EDGE5_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_EDGE4_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_HRSHARE.VALUE=0x00000008
DRIVER.HET.VAR.HET1_BIT30_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT4_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_BIT1_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT8_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT0_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT4_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE7_PIN_SELECT.VALUE=23
DRIVER.HET.VAR.HET2_PWM7_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_PWM3_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET1_PWM3_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_EDGE3_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_PWM1_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM2_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT28_HRSHARE.VALUE=0x00004000
DRIVER.HET.VAR.HET1_BIT26_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP4_PIN_SELECT.VALUE=0
DRIVER.HET.VAR.HET2_PWM0_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_PWM0_PIN_SELECT.VALUE=0
DRIVER.HET.VAR.HET1_BIT21_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT13_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_LR_TIME.VALUE=800.000
DRIVER.HET.VAR.HET2_EDGE0_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM3_ACTION.VALUE=3
DRIVER.HET.VAR.HET1_BIT25_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT21_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT17_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT13_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM5_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_PWM1_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT20_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT11_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT24_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT16_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM5_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_RAM_BASE.VALUE=0xFF440000
DRIVER.HET.VAR.HET2_PWM3_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT7_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM4_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT15_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP4_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT8_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE1_PIN_SELECT.VALUE=11
DRIVER.HET.VAR.HET1_CAP7_PIN_SELECT.VALUE=30
DRIVER.HET.VAR.HET1_BIT31_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT23_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT15_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE7_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_BIT2_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE4_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_BIT15_PULL.VALUE=1
DRIVER.HET.VAR.HET2_BIT10_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP6_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM2_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT18_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_BIT0_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET1_BIT30_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE1_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE2_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_BIT26_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM1_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT16_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT5_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_RAM_PARITY_ENA.VALUE=0x00000005
DRIVER.HET.VAR.HET1_PWM7_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_PWM0_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT1_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_CAP1_PIN_SELECT.VALUE=2
DRIVER.HET.VAR.HET1_PWM7_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_BIT2_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM6_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM4_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT10_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM7_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT10_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE7_PIN_SELECT.VALUE=23
DRIVER.HET.VAR.HET1_BIT24_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT5_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM7_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_PWM7_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_PWM4_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT3_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE5_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_EDGE4_BOTH.VALUE=0
DRIVER.HET.VAR.HET2_PWM1_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT11_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE1_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_LR_PRESCALE.VALUE=7
DRIVER.HET.VAR.HET2_PWM2_PIN_SELECT.VALUE=12
DRIVER.HET.VAR.HET2_BIT1_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT31_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT23_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT15_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM6_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_EDGE2_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT30_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT27_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT22_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT19_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT14_PULL.VALUE=1
DRIVER.HET.VAR.HET2_EDGE2_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_BIT8_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT9_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE1_PIN_SELECT.VALUE=11
DRIVER.HET.VAR.HET1_BIT25_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT17_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_BIT8_PULL.VALUE=1
DRIVER.HET.VAR.HET1_PWM4_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM0_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT14_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT2_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM5_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_BIT0_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_PIN_SELECT.VALUE=17
DRIVER.HET.VAR.HET1_BIT25_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT17_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM6_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT4_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE1_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_CAP7_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT28_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT24_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT16_PULL.VALUE=1
DRIVER.HET.VAR.HET2_BIT12_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_PWM1_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT0_HRSHARE.VALUE=0x00000001
DRIVER.HET.VAR.HET1_EDGE7_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_PWM1_DUTY_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE6_PIN_SELECT.VALUE=22
DRIVER.HET.VAR.HET2_BIT2_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_DIS_BLACKBOX.VALUE=0
DRIVER.HET.VAR.HET1_BIT24_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT16_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE3_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE6_EVENT.VALUE=1
DRIVER.HET.VAR.HET1_EDGE3_BOTH.VALUE=0
DRIVER.HET.VAR.HET1_PWM6_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET1_BIT28_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT13_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_PWM1_DUTY.VALUE=50
DRIVER.HET.VAR.HET1_BIT20_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT12_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_CAP3_PIN_SELECT.VALUE=6
DRIVER.HET.VAR.HET2_BIT10_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT2_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_PWM3_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_EDGE3_EVENT.VALUE=1
DRIVER.HET.VAR.HET2_PWM6_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_PWM3_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET1_BIT3_PULL.VALUE=1
DRIVER.HET.VAR.HET2_PWM1_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT1_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT26_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT6_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM1_DUTYTIME.VALUE=500.622
DRIVER.HET.VAR.HET2_BIT5_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_POLARITY.VALUE=3
DRIVER.HET.VAR.HET2_BIT16_HRSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP0_POLARITY.VALUE=0
DRIVER.HET.VAR.HET1_BIT4_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE0_PIN_SELECT.VALUE=9
DRIVER.HET.VAR.HET2_BIT10_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_CAP6_PIN_SELECT.VALUE=28
DRIVER.HET.VAR.HET1_PWM2_PERIOD.VALUE=1000.000
DRIVER.HET.VAR.HET2_EDGE5_BOTH.VALUE=0
DRIVER.HET.VAR.HET2_BIT13_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT3_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE0_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_PWM7_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_BIT3_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT25_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT17_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_EDGE4_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT31_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT29_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT23_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT15_PULL.VALUE=1
DRIVER.HET.VAR.HET1_BIT0_DIR.VALUE=0x00000001
DRIVER.HET.VAR.HET2_CAP2_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_PWM7_DUTY.VALUE=50
DRIVER.HET.VAR.HET2_BIT6_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM4_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT11_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT26_DOUT.VALUE=0
DRIVER.HET.VAR.HET1_BIT18_DOUT.VALUE=0
DRIVER.HET.VAR.HET2_PWM6_PERIOD_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_ACTION.VALUE=3
DRIVER.HET.VAR.HET2_BIT9_PULL.VALUE=1
DRIVER.HET.VAR.HET1_PWM7_DUTY_LVL.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM3_PERIOD_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM2_ENA.VALUE=0
DRIVER.HET.VAR.HET2_BIT12_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE3_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM7_PIN_SELECT.VALUE=19
DRIVER.HET.VAR.HET2_BIT2_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_CAP0_PIN_SELECT.VALUE=0
DRIVER.HET.VAR.HET1_BIT27_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT19_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT6_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM0_DUTY_PRESCALER.VALUE=45184
DRIVER.HET.VAR.HET2_EDGE7_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET2_PWM0_POLARITY.VALUE=3
DRIVER.HET.VAR.HET1_BIT26_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_XORSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT10_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_EDGE6_PIN_SELECT.VALUE=22
DRIVER.HET.VAR.HET1_PWM0_ACTUALPERIOD.VALUE=1001.244
DRIVER.HET.VAR.HET2_HR_FREQUENCY.VALUE=90.000
DRIVER.HET.VAR.HET2_BIT17_PULL.VALUE=1
DRIVER.HET.VAR.HET2_BIT14_DIR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_PWM5_PERIOD_PRESCALER.VALUE=89984
DRIVER.HET.VAR.HET2_EDGE4_POLARITY.VALUE=0
DRIVER.HET.VAR.HET2_EDGE0_INTENA.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT7_PULDIS.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT4_HRSHARE.VALUE=0x00000004
DRIVER.HET.VAR.HET1_BIT2_ANDSHARE.VALUE=0x00000000
DRIVER.HET.VAR.HET2_BIT4_PDR.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT26_PSL.VALUE=0x00000000
DRIVER.HET.VAR.HET1_BIT18_PSL.VALUE=0x00000000
DRIVER.RTP.VAR.RTP_PORT_BIT1_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT3_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT1_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT5_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT14_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT11_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT3_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT8_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT4_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT6_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT2_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT10_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT6_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT14_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT12_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT17_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT4_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT10_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT2_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT5_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT3_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT11_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT7_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT13_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT5_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT11_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT9_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT6_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT12_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT4_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT12_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT8_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT10_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT15_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT14_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT18_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT6_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT0_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT4_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT12_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT3_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT7_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT5_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT13_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT9_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT11_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT15_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT7_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT13_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT8_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT6_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT14_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT12_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT16_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT16_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT17_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT8_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT1_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT10_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT14_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT4_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT9_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT7_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT15_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT9_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT13_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT10_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT17_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT2_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT9_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT15_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT8_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT16_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT14_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT18_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT17_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_BASE_PORT.VALUE=0xFFFFFA38
DRIVER.RTP.VAR.RTP_PORT_BIT2_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT16_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT5_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT9_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT17_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT15_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT15_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT11_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT17_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT7_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT18_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT0_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT16_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT18_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT3_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT18_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT6_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT17_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT12_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT13_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT18_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT4_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT5_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT7_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT10_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT13_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT18_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT5_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT11_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT8_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT0_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT11_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT14_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT3_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT1_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT6_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT0_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT9_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT2_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT12_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT16_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT15_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT0_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT0_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT1_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_BASE.VALUE=0xFFFFFA00
DRIVER.RTP.VAR.RTP_PORT_BIT3_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT8_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT1_PULDIS.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT1_PDR.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT7_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT2_FUN.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT0_PSL.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT4_DIR.VALUE=0
DRIVER.RTP.VAR.RTP_BASE_RAM.VALUE=0xFFF83000
DRIVER.RTP.VAR.RTP_PORT_BIT13_PULL.VALUE=2
DRIVER.RTP.VAR.RTP_PORT_BIT10_DIR.VALUE=1
DRIVER.RTP.VAR.RTP_PORT_BIT16_DOUT.VALUE=0
DRIVER.RTP.VAR.RTP_PORT_BIT2_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT14_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT17_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT17_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT4_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT1_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT18_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT0_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT15_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT4_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT2_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT7_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT15_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT18_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT0_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_BASE.VALUE=0xFFFFF700
DRIVER.DMM.VAR.DMM_PORT_BIT1_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT3_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT9_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT1_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT2_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT2_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT0_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT5_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT4_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT8_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT16_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT2_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT3_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_BASE_PORT.VALUE=0xFFFFF770
DRIVER.DMM.VAR.DMM_PORT_BIT1_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT5_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT10_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT3_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT4_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT2_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT6_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT6_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT7_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT9_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT17_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT0_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT4_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT5_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT3_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT0_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT7_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT18_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT5_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT6_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT4_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT8_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT7_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT18_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT6_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT10_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT7_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT5_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT5_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT1_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT9_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT12_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT7_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT8_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT16_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT6_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT8_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT10_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT8_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT12_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT9_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT10_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT7_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT2_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT10_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT13_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT9_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT3_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT8_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT9_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT12_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT10_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT14_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT14_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT0_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT12_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT9_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT3_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT11_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT14_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT11_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT15_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT8_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT14_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT1_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT12_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT16_DIR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT1_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT14_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT4_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT12_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT15_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT15_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT12_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT17_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT15_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT16_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT14_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT18_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT2_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT6_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT16_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT5_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT13_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT16_DOUT.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT17_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT15_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT17_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT17_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT10_PULDIS.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT18_FUN.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT16_PSL.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT3_PULL.VALUE=2
DRIVER.DMM.VAR.DMM_PORT_BIT0_DIR.VALUE=1
DRIVER.DMM.VAR.DMM_PORT_BIT18_PDR.VALUE=0
DRIVER.DMM.VAR.DMM_PORT_BIT6_DOUT.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT0_DIR.VALUE=0
DRIVER.I2C.VAR.I2C_STOPBITS.VALUE=2
DRIVER.I2C.VAR.I2C_PORT_BIT1_DIR.VALUE=0
DRIVER.I2C.VAR.I2C_ICXRDYINTLVL.VALUE=0
DRIVER.I2C.VAR.I2C_BASE_PORT.VALUE=0xFFF7D44C
DRIVER.I2C.VAR.I2C_DATACOUNT.VALUE=8
DRIVER.I2C.VAR.I2C_ADDRMODE.VALUE=7BIT_AMODE
DRIVER.I2C.VAR.I2C_PORT_BIT0_FUN.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT0_PDR.VALUE=0
DRIVER.I2C.VAR.I2C_BC_VALUE.VALUE=0x0003
DRIVER.I2C.VAR.I2C_PORT_BIT1_FUN.VALUE=0
DRIVER.I2C.VAR.I2C_RM_ENA.VALUE=0
DRIVER.I2C.VAR.I2C_BC.VALUE=8_BIT
DRIVER.I2C.VAR.I2C_PORT_BIT1_PDR.VALUE=0
DRIVER.I2C.VAR.I2C_TXRX_VALUE.VALUE=0
DRIVER.I2C.VAR.I2C_SCDLVL.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT0_PSL.VALUE=1
DRIVER.I2C.VAR.I2C_STPCND.VALUE=1
DRIVER.I2C.VAR.I2C_ALINTENA.VALUE=0
DRIVER.I2C.VAR.I2C_PRESCALE.VALUE=10
DRIVER.I2C.VAR.I2C_PORT_BIT1_PSL.VALUE=1
DRIVER.I2C.VAR.I2C_TXRX.VALUE=TRANSMITTER
DRIVER.I2C.VAR.I2C_PORT_BIT0_DOUT.VALUE=0
DRIVER.I2C.VAR.I2C_ALINTLVL.VALUE=0
DRIVER.I2C.VAR.I2C_RXDMA.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT0_PULDIS.VALUE=0
DRIVER.I2C.VAR.I2C_BASE.VALUE=0xFFF7D400
DRIVER.I2C.VAR.I2C_ARDYINTENA.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT1_DOUT.VALUE=0
DRIVER.I2C.VAR.I2C_TXDMA.VALUE=0
DRIVER.I2C.VAR.I2C_MSMODE.VALUE=1
DRIVER.I2C.VAR.I2C_ICCH.VALUE=36
DRIVER.I2C.VAR.I2C_AASLVL.VALUE=0
DRIVER.I2C.VAR.I2C_ICCL.VALUE=36
DRIVER.I2C.VAR.I2C_AAS.VALUE=0
DRIVER.I2C.VAR.I2C_BCM.VALUE=0
DRIVER.I2C.VAR.I2C_ADDRMODE_VALUE.VALUE=0x0001
DRIVER.I2C.VAR.I2C_ICRRDYINTENA.VALUE=0
DRIVER.I2C.VAR.I2C_FDF.VALUE=0
DRIVER.I2C.VAR.I2C_ARDYINTLVL.VALUE=0
DRIVER.I2C.VAR.I2C_PARITYENA.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT0_PULL.VALUE=2
DRIVER.I2C.VAR.I2C_LENGTH.VALUE=8
DRIVER.I2C.VAR.I2C_NACKINTENA.VALUE=0
DRIVER.I2C.VAR.I2C_SCD.VALUE=0
DRIVER.I2C.VAR.I2C_PORT_BIT1_PULL.VALUE=2
DRIVER.I2C.VAR.I2C_ICRRDYINTLVL.VALUE=0
DRIVER.I2C.VAR.I2C_STACND.VALUE=1
DRIVER.I2C.VAR.I2C_PORT_BIT1_PULDIS.VALUE=0
DRIVER.I2C.VAR.I2C_ICXRDYINTENA.VALUE=0
DRIVER.I2C.VAR.I2C_NACKINTLVL.VALUE=0
DRIVER.I2C.VAR.I2C_EVENPARITY.VALUE=0
DRIVER.I2C.VAR.I2C_BAUDRATE.VALUE=100
DRIVER.I2C.VAR.I2C_MODCLK.VALUE=8
DRIVER.DCC.VAR.DCC1_ENABLE_KEY.VALUE=10
DRIVER.DCC.VAR.PINMUX_BASE.VALUE=0xFFFFEA00
DRIVER.DCC.VAR.DCC1_DETECTION_TIME.VALUE=2500.00
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE1_VALUE.VALUE=0x0002
DRIVER.DCC.VAR.DCC1_ENABLE_ERROR_INTERRUPT.VALUE=0xA
DRIVER.DCC.VAR.DCC2_ENABLE.VALUE=0xA
DRIVER.DCC.VAR.PINMUX_BASE_PORT.VALUE=0xFFFFEA40
DRIVER.DCC.VAR.DCC2_ENABLE_ERROR_INTERRUPT.VALUE=0xA
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE0_VALUE.VALUE=0x0001
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE0_FREQ.VALUE=0
DRIVER.DCC.VAR.DCC2_VALID0_SEED.VALUE=0
DRIVER.DCC.VAR.DCC2_CLKT_N2HET2_0_FREQ.VALUE=1
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE1_FREQ.VALUE=180
DRIVER.DCC.VAR.DCC2_DETECTION_TIME.VALUE=2500.00
DRIVER.DCC.VAR.DCC2_CLOCK_DRIFT.VALUE=1.0
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE1_VALUE.VALUE=0x0002
DRIVER.DCC.VAR.DCC1_CLKT_N2HET1_31_FREQ.VALUE=1
DRIVER.DCC.VAR.DCC2_COUNT0_SEED.VALUE=0
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE0.VALUE=OSCIN
DRIVER.DCC.VAR.DCC2_CLOCK_SOURCE1.VALUE=VCLK
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE0_FREQ.VALUE=16.0
DRIVER.DCC.VAR.DCC1_VALID0_SEED.VALUE=792
DRIVER.DCC.VAR.DCC1_BASE.VALUE=0xFFFFEC00
DRIVER.DCC.VAR.DCC2_COUNT1_SEED.VALUE=891000
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE1_FREQ.VALUE=180.00
DRIVER.DCC.VAR.DCC1_CLOCK_DRIFT.VALUE=1.0
DRIVER.DCC.VAR.DCC1_ENABLE.VALUE=0xA
DRIVER.DCC.VAR.DCC1_ENABLE_SINGLESHOT_MODE.VALUE=0x5
DRIVER.DCC.VAR.DCC2_ENABLE_SINGLESHOT_MODE.VALUE=0x5
DRIVER.DCC.VAR.DCC2_BASE.VALUE=0xFFFFF400
DRIVER.DCC.VAR.DCC1_DONE_INTERRUPT_ENABLE.VALUE=0xA
DRIVER.DCC.VAR.DCC2_DONE_INTERRUPT_ENABLE.VALUE=0xA
DRIVER.DCC.VAR.DCC2_ENABLE_KEY.VALUE=0x5
DRIVER.DCC.VAR.DCC1_COUNT0_SEED.VALUE=39204
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE0_VALUE.VALUE=0x0001
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE0.VALUE=OSCIN
DRIVER.DCC.VAR.DCC1_CLOCK_SOURCE1.VALUE=PLL1
DRIVER.DCC.VAR.CLKT_TCK_FREQ.VALUE=12.0
DRIVER.DCC.VAR.DCC1_COUNT1_SEED.VALUE=445500
DRIVER.PINMUX.VAR.DMA_EIDXS_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_2.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_2.VALUE=0
DRIVER.PINMUX.VAR.MUX61_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX7_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_3.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_3.VALUE=0
DRIVER.PINMUX.VAR.MUX61_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX7_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_4.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_4.VALUE=0
DRIVER.PINMUX.VAR.MUX61_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX50_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX42_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX34_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX26_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX18_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_5.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_5.VALUE=0
DRIVER.PINMUX.VAR.MUX61_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_6.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_6.VALUE=0
DRIVER.PINMUX.VAR.MUX61_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_7.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_7.VALUE=0
DRIVER.PINMUX.VAR.MUX53_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX45_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX37_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX29_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_8.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_10.VALUE=1
DRIVER.PINMUX.VAR.MUX99_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_96_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_88_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_5_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_9.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_FIDXD_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_12.VALUE=1
DRIVER.PINMUX.VAR.MUX30_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_13.VALUE=1
DRIVER.PINMUX.VAR.MUX30_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_14.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_10.VALUE=1
DRIVER.PINMUX.VAR.MUX30_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_81_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_73_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_65_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_57_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_49_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTLFSEN_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTBTCEN_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_CHPR_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX30_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_16.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_12.VALUE=1
DRIVER.PINMUX.VAR.MUX30_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTBTCEN_13.VALUE=1
DRIVER.PINMUX.VAR.MUX30_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX22_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX14_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTBTCEN_14.VALUE=1
DRIVER.PINMUX.VAR.MUX101_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_50_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_42_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_34_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_26_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_18_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTBTCEN_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_ENABLEINT_1.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_16.VALUE=1
DRIVER.PINMUX.VAR.DMA_ENABLEINT_2.VALUE=1
DRIVER.PINMUX.VAR.DMA_ENABLEINT_3.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_10.VALUE=FIXED
DRIVER.PINMUX.VAR.DMA_ENABLEINT_4.VALUE=1
DRIVER.PINMUX.VAR.PINMUX10.VALUE="PINMUX_BALL_N19_AD1EVT | PINMUX_BALL_N15_ETMDATA_19 | PINMUX_BALL_N17_EMIF_nCS_0 | PINMUX_BALL_M15_ETMDATA_18"
DRIVER.PINMUX.VAR.MUX11_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_11_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_PRITY_11.VALUE=FIXED
DRIVER.PINMUX.VAR.DMA_CHPR_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.PINMUX11.VALUE="PINMUX_BALL_K17_EMIF_nCS_3 | PINMUX_BALL_M17_EMIF_nCS_4 | PINMUX_BALL_L15_ETMDATA_17 | PINMUX_BALL_P1_HET1_24"
DRIVER.PINMUX.VAR.DMA_PRITY_12.VALUE=FIXED
DRIVER.PINMUX.VAR.PINMUX20.VALUE="PINMUX_BALL_C11_EMIF_ADDR_13 | PINMUX_BALL_C10_EMIF_ADDR_12 | PINMUX_BALL_F3_MIBSPI1NCS_1 | PINMUX_BALL_C9_EMIF_ADDR_11"
DRIVER.PINMUX.VAR.PINMUX12.VALUE="PINMUX_BALL_A14_HET1_26 | PINMUX_BALL_K15_ETMDATA_16 | PINMUX_BALL_G19_MIBSPI1NENA | PINMUX_BALL_H18_MIBSPI5NENA"
DRIVER.PINMUX.VAR.DMA_PRITY_13.VALUE=FIXED
DRIVER.PINMUX.VAR.PINMUX21.VALUE="PINMUX_BALL_D5_EMIF_ADDR_1 | PINMUX_BALL_C8_EMIF_ADDR_10 | PINMUX_BALL_C7_EMIF_ADDR_9"
DRIVER.PINMUX.VAR.PINMUX13.VALUE="PINMUX_BALL_J18_MIBSPI5SOMI_0 | PINMUX_BALL_J19_MIBSPI5SIMO_0 | PINMUX_BALL_H19_MIBSPI5CLK | PINMUX_BALL_R2_MIBSPI1NCS_0"
DRIVER.PINMUX.VAR.DMA_PRITY_14.VALUE=FIXED
DRIVER.PINMUX.VAR.PINMUX30.VALUE=0
DRIVER.PINMUX.VAR.PINMUX22.VALUE="PINMUX_BALL_D4_EMIF_ADDR_0 | PINMUX_BALL_C5_EMIF_ADDR_7 | PINMUX_BALL_C4_EMIF_ADDR_6 | PINMUX_BALL_E6_ETMDATA_11"
DRIVER.PINMUX.VAR.PINMUX14.VALUE="PINMUX_BALL_E18_HET1_08 | PINMUX_BALL_K19_HET1_28 | PINMUX_BALL_D17_EMIF_nWE | PINMUX_BALL_D16_EMIF_BA_1"
DRIVER.PINMUX.VAR.MUX92_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX84_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX76_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX68_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_15.VALUE=FIXED
DRIVER.PINMUX.VAR.PINMUX31.VALUE=0
DRIVER.PINMUX.VAR.PINMUX23.VALUE=PINMUX_BALL_C6_EMIF_ADDR_8
DRIVER.PINMUX.VAR.PINMUX15.VALUE="PINMUX_BALL_C17_EMIF_ADDR_21 | PINMUX_BALL_C16_EMIF_ADDR_20 | PINMUX_BALL_C15_EMIF_ADDR_19 | PINMUX_BALL_D15_EMIF_ADDR_18"
DRIVER.PINMUX.VAR.DMA_PRITY_16.VALUE=FIXED
DRIVER.PINMUX.VAR.PINMUX32.VALUE=0
DRIVER.PINMUX.VAR.PINMUX16.VALUE="PINMUX_BALL_E13_ETMDATA_12 | PINMUX_BALL_C14_EMIF_ADDR_17 | PINMUX_BALL_D14_EMIF_ADDR_16 | PINMUX_BALL_E12_ETMDATA_13"
DRIVER.PINMUX.VAR.PINMUX33.VALUE=0
DRIVER.PINMUX.VAR.PINMUX17.VALUE="PINMUX_BALL_D19_HET1_10 | PINMUX_BALL_E11_ETMDATA_14 | PINMUX_BALL_B4_HET1_12 | PINMUX_BALL_E9_ETMDATA_08"
DRIVER.PINMUX.VAR.PINMUX34.VALUE=0
DRIVER.PINMUX.VAR.PINMUX26.VALUE="PINMUX_BALL_W6_MIBSPI5NCS_2 | PINMUX_BALL_T12_MIBSPI5NCS_3"
DRIVER.PINMUX.VAR.PINMUX18.VALUE="PINMUX_BALL_C13_EMIF_ADDR_15 | PINMUX_BALL_C12_EMIF_ADDR_14 | PINMUX_BALL_M2_GIOB_0"
DRIVER.PINMUX.VAR.DMA_ADDMR_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.PINMUX35.VALUE=0
DRIVER.PINMUX.VAR.PINMUX27.VALUE="PINMUX_BALL_E19_MIBSPI5NCS_0 | PINMUX_BALL_B6_MIBSPI5NCS_1 | PINMUX_BALL_E16_MIBSPI5SIMO_1 | PINMUX_BALL_H17_MIBSPI5SIMO_2"
DRIVER.PINMUX.VAR.PINMUX19.VALUE="PINMUX_BALL_E8_ETMDATA_09 | PINMUX_BALL_B11_HET1_30 | PINMUX_BALL_E10_ETMDATA_15 | PINMUX_BALL_E7_ETMDATA_10"
DRIVER.PINMUX.VAR.PINMUX28.VALUE="PINMUX_BALL_G17_MIBSPI5SIMO_3 | PINMUX_BALL_E17_MIBSPI5SOMI_1 | PINMUX_BALL_H16_MIBSPI5SOMI_2 | PINMUX_BALL_G16_MIBSPI5SOMI_3"
DRIVER.PINMUX.VAR.MUX98_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.PINMUX29.VALUE=PINMUX_BALL_D3_SPI2NENA
DRIVER.PINMUX.VAR.MUX98_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_10.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX98_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX7_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_11.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_PRITY_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX98_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_20.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_12.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX100_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX98_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_21.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_13.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX100_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_30.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_22.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_14.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_0.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_0.VALUE=ENABLED
DRIVER.PINMUX.VAR.MUX100_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_31.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_23.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_15.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_TTYPE_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_1.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ENABLEREG_1.VALUE=1
DRIVER.PINMUX.VAR.MUX100_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_24.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_16.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_2.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ENABLEREG_2.VALUE=1
DRIVER.PINMUX.VAR.MUX100_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX91_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX83_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX75_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX67_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX59_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_25.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_17.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_3.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ENABLEREG_3.VALUE=1
DRIVER.PINMUX.VAR.MUX91_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX83_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX75_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX67_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX59_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_26.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_18.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_4.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ENABLEREG_4.VALUE=1
DRIVER.PINMUX.VAR.MUX91_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX83_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX75_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX67_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX61_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX59_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX53_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX45_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX37_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX29_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_102_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_27.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_19.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_5.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ADDMR_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX91_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX83_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX75_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX67_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX59_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_28.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_6.VALUE=ENABLED
DRIVER.PINMUX.VAR.MUX91_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX83_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX75_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX67_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX59_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX6_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_29.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_FIDXS_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_7.VALUE=ENABLED
DRIVER.PINMUX.VAR.MUX6_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_8.VALUE=ENABLED
DRIVER.PINMUX.VAR.MUX6_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_9.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX6_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX60_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX6_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX60_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX6_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_10.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_10.VALUE=0
DRIVER.PINMUX.VAR.MUX60_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_11.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTASS_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX60_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_20.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_12.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_12.VALUE=0
DRIVER.PINMUX.VAR.MUX60_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_21.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_13.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_13.VALUE=0
DRIVER.PINMUX.VAR.MUX52_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX44_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX36_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX28_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_30.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_22.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_14.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_14.VALUE=0
DRIVER.PINMUX.VAR.MUX104_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_94_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_86_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_78_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_3_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_31.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_23.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_15.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_PRITY_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ACC_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_STADD_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_24.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_16.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_STADD_2.VALUE=0
DRIVER.PINMUX.VAR.MUX21_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_25.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_17.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_STADD_3.VALUE=0
DRIVER.PINMUX.VAR.MUX21_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_26.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_18.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_STADD_4.VALUE=0
DRIVER.PINMUX.VAR.MUX30_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX22_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX21_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX14_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_71_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_63_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_55_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_47_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_39_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_27.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_19.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX21_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_28.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_28.VALUE=0
DRIVER.PINMUX.VAR.MUX21_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_29.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_29.VALUE=0
DRIVER.PINMUX.VAR.MUX21_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX13_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_0.VALUE=0
DRIVER.PINMUX.VAR.MUX95_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX87_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX79_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_40_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_32_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_24_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_16_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_0.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_TTYPE_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_1.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_2.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_3.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_4.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_0.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_5.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_1.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMR_6.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_2.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMR_7.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_3.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMR_8.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_4.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMR_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_9.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_5.VALUE=8BIT
DRIVER.PINMUX.VAR.GATE_EMIF_CLK.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_6.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX97_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX89_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_7.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX97_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX89_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_8.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX97_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX89_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX80_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX72_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX64_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX56_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX48_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_9.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMW_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX97_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX89_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX97_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX89_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_107_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX90_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX82_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX74_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX66_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX58_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_DEBUGMODE.VALUE=IGNORE_SUSPEND
DRIVER.PINMUX.VAR.MUX90_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX82_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX74_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX66_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX58_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_0.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ERRACT.VALUE=IGNORE
DRIVER.PINMUX.VAR.MUX90_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX82_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX74_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX66_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX58_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX3_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_100_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_1.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CHPR_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX90_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX82_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX74_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX66_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX58_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_2.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_BASE_PORT.VALUE=0xFFFFF040
DRIVER.PINMUX.VAR.MUX90_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX82_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX74_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX66_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX58_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX5_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_3.VALUE=CONSTANT
DRIVER.PINMUX.VAR.MUX5_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_4.VALUE=CONSTANT
DRIVER.PINMUX.VAR.MUX5_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_5.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTFTCEN_1.VALUE=1
DRIVER.PINMUX.VAR.MUX5_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_6.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTFTCEN_2.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX5_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_7.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTFTCEN_3.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX5_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_8.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTEN_10.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTFTCEN_4.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX41_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX33_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX25_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX17_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_99_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_8_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_9.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTEN_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTFTCEN_5.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_12.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTFTCEN_6.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_13.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTFTCEN_7.VALUE=1
DRIVER.PINMUX.VAR.MUX51_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX43_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX35_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX27_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX19_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_14.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTFTCEN_8.VALUE=1
DRIVER.PINMUX.VAR.ALT_ADC_SELECT.VALUE=1
DRIVER.PINMUX.VAR.MUX98_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_92_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_84_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_76_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_68_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_1_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTFTCEN_9.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTEN_16.VALUE=1
DRIVER.PINMUX.VAR.MUX20_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX20_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX20_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_61_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_53_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_45_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_37_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_29_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX20_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX20_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX20_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX12_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX100_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_30_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_22_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_14_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX10_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ENDADD_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ENDADD_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_ENDADD_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ENDADD_4.VALUE=0
DRIVER.PINMUX.VAR.ETHERNET_SELECT.VALUE=RMII
DRIVER.PINMUX.VAR.MUX91_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX83_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX75_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX67_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX59_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTASS_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTASS_1.VALUE=TO_VIM
DRIVER.PINMUX.VAR.DMA_INTASS_2.VALUE=TO_VIM
DRIVER.PINMUX.VAR.CONCOUNT.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTASS_3.VALUE=TO_VIM
DRIVER.PINMUX.VAR.DMA_INTASS_4.VALUE=TO_VIM
DRIVER.PINMUX.VAR.DMA_ADDMR_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHAS_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ACC_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHAS_2.VALUE=0
DRIVER.PINMUX.VAR.MUX96_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX88_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_3.VALUE=0
DRIVER.PINMUX.VAR.MUX96_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX88_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_10.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_0.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_CHAS_4.VALUE=0
DRIVER.PINMUX.VAR.MUX96_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX88_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX6_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_11.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ADDMW_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_1.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_INTMP_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHAS_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX96_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX88_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_20.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_12.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_2.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_CHAS_6.VALUE=0
DRIVER.PINMUX.VAR.MUX96_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX88_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_21.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_13.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_3.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_CHAS_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_30.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_22.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_14.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_4.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_CHAS_8.VALUE=0
DRIVER.PINMUX.VAR.GIOB_DISABLE_SELECT.VALUE=OFF
DRIVER.PINMUX.VAR.PIN_MUX_105_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_31.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_23.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ADDMW_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_15.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_ADDMR_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_5.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_TRIG_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHAS_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_24.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_16.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_6.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX81_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX73_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX65_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_25.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_17.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_7.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX81_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX73_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX65_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_26.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_18.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_8.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_0.VALUE=0
DRIVER.PINMUX.VAR.MUX81_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX73_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX65_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX60_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX52_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX44_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX36_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX28_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXS_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_27.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_FIDXS_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_19.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_9.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ENABLE1.VALUE=1
DRIVER.PINMUX.VAR.MUX81_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX73_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX65_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_28.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_2.VALUE=0
DRIVER.PINMUX.VAR.MUX81_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX73_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX65_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX4_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXS_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_29.VALUE=ENABLED
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_3.VALUE=0
DRIVER.PINMUX.VAR.MUX57_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX49_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX4_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_10.VALUE=1
DRIVER.PINMUX.VAR.MUX4_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_BYP_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_1.VALUE=1
DRIVER.PINMUX.VAR.MUX4_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_12.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_2.VALUE=1
DRIVER.PINMUX.VAR.MUX50_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX4_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_13.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_3.VALUE=1
DRIVER.PINMUX.VAR.MUX50_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX4_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_14.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_4.VALUE=1
DRIVER.PINMUX.VAR.MUX50_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_97_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_89_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_6_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_IFT_COUNT_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_5.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_1.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX50_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_BYP_16.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTBTCEN_6.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_2.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX50_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTBTCEN_7.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_3.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX50_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX42_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX34_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX26_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX18_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTBTCEN_8.VALUE=1
DRIVER.PINMUX.VAR.DMA_PRITY_4.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX103_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_90_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_82_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_74_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_66_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_58_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTBTCEN_9.VALUE=1
DRIVER.PINMUX.VAR.DMA_TRIG_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_5.VALUE=FIXED
DRIVER.PINMUX.VAR.DMA_PRITY_6.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX11_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_PRITY_7.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX11_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_PRITY_8.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX21_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX13_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX11_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_51_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_43_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_35_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_27_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_19_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_9.VALUE=FIXED
DRIVER.PINMUX.VAR.MUX11_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX11_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.AD1.VALUE=0
DRIVER.PINMUX.VAR.MUX11_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.AD2.VALUE=0
DRIVER.PINMUX.VAR.MUX94_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX86_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX78_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_20_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_12_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX9_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX104_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX104_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX104_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_PRITY_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.ALT_ADC.VALUE=0
DRIVER.PINMUX.VAR.I2C.VALUE=0
DRIVER.PINMUX.VAR.MUX104_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX104_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX95_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX87_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX79_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX95_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX87_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX79_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX95_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX87_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX79_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX71_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX63_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX55_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX47_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX39_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_DEBUGMODE_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX95_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX87_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX79_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX95_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX87_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX79_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_10.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_103_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_12.VALUE=0
DRIVER.PINMUX.VAR.MUX80_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX72_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX64_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX56_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_13.VALUE=0
DRIVER.PINMUX.VAR.HET1.VALUE=0
DRIVER.PINMUX.VAR.MUX80_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX72_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX64_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX56_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_10.VALUE=0
DRIVER.PINMUX.VAR.HET2.VALUE=0
DRIVER.PINMUX.VAR.MUX80_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX72_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX64_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX56_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX2_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_1.VALUE=1
DRIVER.PINMUX.VAR.MUX80_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX72_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX64_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX56_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_2.VALUE=1
DRIVER.PINMUX.VAR.MUX80_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX72_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX64_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX56_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX3_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_3.VALUE=1
DRIVER.PINMUX.VAR.MUX56_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX48_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX3_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_10.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTEN_4.VALUE=1
DRIVER.PINMUX.VAR.MUX3_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXD_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXD_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_11.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_AIM_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTEN_5.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTASS_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX3_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_20.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_12.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTEN_6.VALUE=1
DRIVER.PINMUX.VAR.EMIF.VALUE=0
DRIVER.PINMUX.VAR.MUX41_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX3_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_CP0_ISADDR_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_21.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_13.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTEN_7.VALUE=1
DRIVER.PINMUX.VAR.MUX41_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX3_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_30.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_22.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_14.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_8.VALUE=1
DRIVER.PINMUX.VAR.MUX41_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX40_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX32_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX24_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX16_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_95_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_87_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_79_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_4_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_31.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_23.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_15.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMR_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_IET_COUNT_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTEN_9.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ACC_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX41_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_24.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_16.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_12.VALUE=0
DRIVER.PINMUX.VAR.MUX41_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_25.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_17.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_13.VALUE=0
DRIVER.PINMUX.VAR.MUX41_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX33_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX25_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX17_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_26.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_18.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_10.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX97_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX89_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_80_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_72_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_64_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_56_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_48_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_27.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_19.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_11.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_ADDMW_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.GIOA.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_28.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_20.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_12.VALUE=8BIT
DRIVER.PINMUX.VAR.GIOB.VALUE=0
DRIVER.PINMUX.VAR.MUX10_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_29.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_21.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_13.VALUE=8BIT
DRIVER.PINMUX.VAR.GIOB_DISABLE.VALUE=0
DRIVER.PINMUX.VAR.MUX10_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_30.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_22.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_14.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX10_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_41_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_33_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_25_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_17_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_31.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_IET_COUNT_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_23.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_15.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_CHANNEL_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX10_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_28.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_24.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_16.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX10_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_29.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_25.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_17.VALUE=8BIT
DRIVER.PINMUX.VAR.MUX10_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_26.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_18.VALUE=8BIT
DRIVER.PINMUX.VAR.PIN_MUX_10_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_27.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_19.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_BYP_1.VALUE=1
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_28.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_BYP_2.VALUE=1
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_29.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_BYP_3.VALUE=1
DRIVER.PINMUX.VAR.DMA_BYP_4.VALUE=1
DRIVER.PINMUX.VAR.DMA_AIM_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_BYP_5.VALUE=1
DRIVER.PINMUX.VAR.DMA_BYP_6.VALUE=1
DRIVER.PINMUX.VAR.DMA_BYP_7.VALUE=1
DRIVER.PINMUX.VAR.DMA_BYP_8.VALUE=1
DRIVER.PINMUX.VAR.MUX90_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX82_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX74_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX66_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX58_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_BYP_9.VALUE=1
DRIVER.PINMUX.VAR.MIBSPI1.VALUE=0
DRIVER.PINMUX.VAR.MUX103_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MIBSPI3.VALUE=0
DRIVER.PINMUX.VAR.MUX103_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.OHCI0.VALUE=0
DRIVER.PINMUX.VAR.MUX103_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MIBSPI5.VALUE=0
DRIVER.PINMUX.VAR.DMM.VALUE=0
DRIVER.PINMUX.VAR.W2FC.VALUE=0
DRIVER.PINMUX.VAR.OHCI1.VALUE=0
DRIVER.PINMUX.VAR.MUX103_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX103_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX94_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX86_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX78_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX94_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX86_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX78_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX94_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX86_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX78_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX5_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_108_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX94_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX86_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX78_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX94_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX86_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX78_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX9_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX9_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_10.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX9_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_101_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXD_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_11.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_PRITY_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX9_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_12.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX71_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX63_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX9_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXD_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_13.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX71_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX63_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX9_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_10.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_0.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_14.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX71_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX63_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX51_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX43_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX35_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX27_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX19_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_11.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXD_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_15.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX71_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX63_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_20.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_12.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_16.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX71_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX63_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX2_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_21.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_13.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_3.VALUE=0
DRIVER.PINMUX.VAR.MUX55_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX47_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX39_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX2_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_30.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_22.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_14.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_EIDXD_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_10.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.MUX2_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_9_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_31.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CHANNEL_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_23.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CHANNEL_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_15.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_CHANNEL_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXD_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_IET_COUNT_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_11.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.MUX2_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_24.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_16.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_12.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.MUX40_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX2_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_25.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_17.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_13.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.ETM.VALUE=0
DRIVER.PINMUX.VAR.MUX40_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX2_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_26.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_18.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_14.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTFTCEN_10.VALUE=1
DRIVER.PINMUX.VAR.MUX40_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_93_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_85_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_77_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_69_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_2_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_27.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_ADDMW_19.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_IET_COUNT_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_15.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_PRITY_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTFTCEN_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX40_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_28.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_TRIG_16.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTFTCEN_12.VALUE=1
DRIVER.PINMUX.VAR.MUX40_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_29.VALUE=CONSTANT
DRIVER.PINMUX.VAR.DMA_INTFTCEN_13.VALUE=1
DRIVER.PINMUX.VAR.MUX40_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX32_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX24_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX16_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTFTCEN_14.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTHBCEN_10.VALUE=1
DRIVER.PINMUX.VAR.MUX102_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_70_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_62_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_54_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_46_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_38_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTFTCEN_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTHBCEN_11.VALUE=1
DRIVER.PINMUX.VAR.DMA_CHPR_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ERRACT_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MII.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTFTCEN_16.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTHBCEN_12.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTHBCEN_13.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTHBCEN_14.VALUE=1
DRIVER.PINMUX.VAR.GATE_EMIF_CLK_SELECT.VALUE=OFF
DRIVER.PINMUX.VAR.MUX20_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX12_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_31_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_23_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_15_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTHBCEN_15.VALUE=1
DRIVER.PINMUX.VAR.DMA_TRIG_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHPR_1.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_INTHBCEN_16.VALUE=1
DRIVER.PINMUX.VAR.DMA_CHPR_2.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_CHPR_3.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_INTMP_10.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_4.VALUE=HIGH
DRIVER.PINMUX.VAR.MUX93_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX85_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX77_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX69_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_29_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_11.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_5.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_TRIG_1.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTMP_12.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_6.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_TRIG_2.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTMP_13.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_7.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_TRIG_3.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTMP_14.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_8.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_TRIG_4.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_CHANNEL_28_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_6_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_15.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_CHPR_9.VALUE=HIGH
DRIVER.PINMUX.VAR.DMA_TRIG_5.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTHBCEN_1.VALUE=1
DRIVER.PINMUX.VAR.SCI.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTMP_16.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_TRIG_6.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTHBCEN_2.VALUE=1
DRIVER.PINMUX.VAR.DMA_TRIG_7.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTHBCEN_3.VALUE=1
DRIVER.PINMUX.VAR.DMA_TRIG_8.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTHBCEN_4.VALUE=1
DRIVER.PINMUX.VAR.MUX8_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_9_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_9.VALUE=HARDWARE_TRIGGER
DRIVER.PINMUX.VAR.DMA_INTHBCEN_5.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_1.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_INTHBCEN_6.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_2.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_ENABLEPAR.VALUE=1
DRIVER.PINMUX.VAR.MUX102_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTHBCEN_7.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_3.VALUE=GROUP_A
DRIVER.PINMUX.VAR.MUX102_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTHBCEN_8.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_4.VALUE=GROUP_A
DRIVER.PINMUX.VAR.MUX102_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTHBCEN_9.VALUE=1
DRIVER.PINMUX.VAR.DMA_CHPR_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_5.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_TRIG_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ACC_1.VALUE=ALL
DRIVER.PINMUX.VAR.MUX102_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTMP_6.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_ACC_2.VALUE=ALL
DRIVER.PINMUX.VAR.MUX102_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX93_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX85_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX77_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX69_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTMP_7.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_ACC_3.VALUE=ALL
DRIVER.PINMUX.VAR.MUX93_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX85_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX77_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX69_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTMP_8.VALUE=GROUP_A
DRIVER.PINMUX.VAR.DMA_ACC_4.VALUE=ALL
DRIVER.PINMUX.VAR.MUX93_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX85_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX77_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX70_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX69_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX62_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX54_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX46_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX38_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_106_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_0_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTMP_9.VALUE=GROUP_A
DRIVER.PINMUX.VAR.MUX93_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX85_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX77_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX69_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX93_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX85_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX77_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX69_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX8_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX8_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX8_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_BASE.VALUE=0xFFFFF000
DRIVER.PINMUX.VAR.MUX8_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX70_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX62_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX8_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX70_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX62_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX8_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX70_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX62_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX1_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_20_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_12_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX70_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX62_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX70_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX62_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX1_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX54_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX46_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX38_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX1_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX1_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_98_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_7_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX1_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX31_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX1_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX31_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX1_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX31_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX31_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX23_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX15_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_91_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_83_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_75_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_67_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_59_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TRIG_8_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX31_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX31_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX31_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX23_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.MUX15_OPTION5.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_0.VALUE=0
DRIVER.PINMUX.VAR.MUX96_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX88_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_60_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_52_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_44_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_36_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_28_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_2.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_0.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_21_SELECT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_13_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_21_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_13_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMW_10_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMR_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_1.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHPR_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_PRITY_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_BASE_RAM.VALUE=0xFFF80000
DRIVER.PINMUX.VAR.DMA_EIDXS_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_2.VALUE=0
DRIVER.PINMUX.VAR.RTP.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_3.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_4.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_25_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_AIM_17_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TTYPE_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_5.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_1.VALUE=1
DRIVER.PINMUX.VAR.DMA_FIDXD_6.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_2.VALUE=1
DRIVER.PINMUX.VAR.SPI2.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_7.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_3.VALUE=1
DRIVER.PINMUX.VAR.DMA_FIDXD_8.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_4.VALUE=1
DRIVER.PINMUX.VAR.SPI4.VALUE=0
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_30_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_24_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_22_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_16_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_9.VALUE=0
DRIVER.PINMUX.VAR.DMA_AIM_2_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHAS_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_5.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTASS_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.RMII.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHAS_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_6.VALUE=1
DRIVER.PINMUX.VAR.PINMUX0.VALUE="PINMUX_BALL_C3_MIBSPI3NCS_3 | PINMUX_BALL_B2_MIBSPI3NCS_2"
DRIVER.PINMUX.VAR.MUX99_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_INTLFSEN_7.VALUE=1
DRIVER.PINMUX.VAR.PINMUX1.VALUE="PINMUX_BALL_E3_HET1_11 | PINMUX_BALL_E5_ETMDATA_20 | PINMUX_BALL_F5_ETMDATA_21"
DRIVER.PINMUX.VAR.MUX99_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_10.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_INTLFSEN_8.VALUE=1
DRIVER.PINMUX.VAR.PINMUX2.VALUE="PINMUX_BALL_C1_GIOA_2 | PINMUX_BALL_G5_ETMDATA_22 | PINMUX_BALL_E1_GIOA_3 | PINMUX_BALL_B5_GIOA_5"
DRIVER.PINMUX.VAR.MUX99_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX81_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX73_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX65_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX57_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.MUX49_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_11.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_PRITY_14_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_INTLFSEN_9.VALUE=1
DRIVER.PINMUX.VAR.DMA_INTMP_5_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ACC_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.PINMUX3.VALUE="PINMUX_BALL_K5_ETMDATA_23 | PINMUX_BALL_H3_GIOA_6 | PINMUX_BALL_L5_ETMDATA_24"
DRIVER.PINMUX.VAR.MUX99_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_20.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_12.VALUE=8BIT
DRIVER.PINMUX.VAR.PINMUX4.VALUE="PINMUX_BALL_M1_GIOA_7 | PINMUX_BALL_M5_ETMDATA_25 | PINMUX_BALL_V2_HET1_01 | PINMUX_BALL_U1_HET1_03"
DRIVER.PINMUX.VAR.MUX101_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX99_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_21.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_13.VALUE=8BIT
DRIVER.PINMUX.VAR.PINMUX5.VALUE="PINMUX_BALL_K18_HET1_0 | PINMUX_BALL_W5_HET1_02 | PINMUX_BALL_V6_HET1_05 | PINMUX_BALL_N5_ETMDATA_26"
DRIVER.PINMUX.VAR.MUX101_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_30.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_22.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_14.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_0.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.PINMUX6.VALUE="PINMUX_BALL_T1_HET1_07 | PINMUX_BALL_P5_ETMDATA_27 | PINMUX_BALL_V7_HET1_09 | PINMUX_BALL_R5_ETMDATA_28"
DRIVER.PINMUX.VAR.MUX101_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_31.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_23.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_15.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_1_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_1.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHPR_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.PINMUX7.VALUE="PINMUX_BALL_R6_ETMDATA_29 | PINMUX_BALL_V5_MIBSPI3NCS_1 | PINMUX_BALL_W3_HET1_06 | PINMUX_BALL_R7_ETMDATA_30"
DRIVER.PINMUX.VAR.MUX101_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_24.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_20.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_16.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_12.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_2.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.PINMUX8.VALUE="PINMUX_BALL_N2_HET1_13 | PINMUX_BALL_G3_MIBSPI1NCS_2 | PINMUX_BALL_N1_HET1_15 | PINMUX_BALL_R8_ETMDATA_31"
DRIVER.PINMUX.VAR.MUX101_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX92_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX84_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX76_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.MUX68_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_25.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_21.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_17.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_13.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_3.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.PINMUX9.VALUE="PINMUX_BALL_R9_ETMTRACECLKIN | PINMUX_BALL_W9_MIBSPI3NENA | PINMUX_BALL_V10_MIBSPI3NCS_0 | PINMUX_BALL_J3_MIBSPI1NCS_3"
DRIVER.PINMUX.VAR.MUX92_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX84_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX76_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.MUX68_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_30.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_26.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_22.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_18.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_14.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_4.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX92_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX84_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX76_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX68_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.MUX4_CONFLICT.VALUE=0
DRIVER.PINMUX.VAR.PIN_MUX_104_SELECT.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_31.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_31_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_27.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_26_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_23.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_23_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_19.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_18_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_15.VALUE=0
DRIVER.PINMUX.VAR.DMA_ADDMW_15_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_ADDMR_7_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_CHANNEL_5.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_CHANNEL_4_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_WRITE_ELSIZE_3_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_TRIG_11_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.MUX92_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX84_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX76_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.MUX68_OPTION3.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_28.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_24.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_16.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_6.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX92_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX84_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX76_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX68_OPTION4.VALUE=0
DRIVER.PINMUX.VAR.MUX7_OPTION0.VALUE=0
DRIVER.PINMUX.VAR.DMA_READ_ELSIZE_29.VALUE=8BIT
DRIVER.PINMUX.VAR.DMA_EIDXS_25.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_17.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_7.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.MUX7_OPTION1.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_26.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_18.VALUE=0
DRIVER.PINMUX.VAR.DMA_FIDXD_10.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_8.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_TTYPE_0.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_0.VALUE=0
DRIVER.PINMUX.VAR.MUX7_OPTION2.VALUE=0
DRIVER.PINMUX.VAR.DMA_EIDXS_27.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_27_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_EIDXS_19.VALUE=0
DRIVER.PINMUX.VAR.DMA_TTYPE_19_VALUE.VALUE=0x0001
DRIVER.PINMUX.VAR.DMA_FIDXD_11.VALUE=0
DRIVER.PINMUX.VAR.DMA_CHANNEL_9.VALUE=CHANNEL0
DRIVER.PINMUX.VAR.DMA_TTYPE_1.VALUE=FRAME_TRANSFER
DRIVER.PINMUX.VAR.DMA_CP0_IDADDR_1.VALUE=0
DRIVER.PINMUX.VAR.MUX7_OPTION3.VALUE=0
DRIVER.CRC.VAR.CRC_CH2_PSIH.VALUE=0
DRIVER.CRC.VAR.HTU_CPB_7_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH2_PSIL.VALUE=0
DRIVER.CRC.VAR.HTU_DCP0_TRDIR_1.VALUE=HET_TO_MAIN_MEM
DRIVER.CRC.VAR.CRC_CH1_CCI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPBL_7_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_5_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPB_1_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_DEBMOD_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH1_CFI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPAL_1_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ENABUS_1.VALUE=0
DRIVER.CRC.VAR.HTU_CPA_2_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH2_WDTO.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH2_CCI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_MP1_ACC_1.VALUE=READ_ONLY
DRIVER.CRC.VAR.HTU_CONTPAR_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH2_CFI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPB_6_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_DCP0_EC_1.VALUE=0
DRIVER.CRC.VAR.HTU_DCP0_CPBFULADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPAL_6_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_CPA_7_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_CPB_3_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH1_DTE.VALUE=0
DRIVER.CRC.VAR.HTU_DCP0_FC_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH1_CVH.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH1_PSSIH.VALUE=0
DRIVER.CRC.VAR.HTU_BASE.VALUE=0xFFF7A400
DRIVER.CRC.VAR.CRC_CH1_CVL.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH1_PSSIL.VALUE=0
DRIVER.CRC.VAR.HTU_ICPBL_3_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_1_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH2_DTE.VALUE=1
DRIVER.CRC.VAR.CRC_CH2_CVH.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH2_CVL.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH1_PCP.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPA_6_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPB_2_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH1_SCP.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_DCP0_CPAFULADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPAL_2_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.CRC_CH2_PCP.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_CPA_3_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH1_PSA.VALUE=1
DRIVER.CRC.VAR.CRC_CH1_ORI.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH2_SCP.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_MP1_STADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPB_7_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH1_TOE.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPAL_7_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.CRC_CH2_PSA.VALUE=1
DRIVER.CRC.VAR.CRC_CH2_ORI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_CPB_4_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_MP0_ENA_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH2_MODE_VALUE.VALUE=0x0001
DRIVER.CRC.VAR.CRC_CH1_URI.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH2_PSSIH.VALUE=0
DRIVER.CRC.VAR.CRC_CH2_TOE.VALUE=0x00000000
DRIVER.CRC.VAR.CRC_CH2_PSSIL.VALUE=0
DRIVER.CRC.VAR.CRC_CH1_BCTO.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_ICPBL_4_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_2_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_PAR_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH2_URI.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_CONT_1.VALUE=0
DRIVER.CRC.VAR.HTU_ENAREQ_1.VALUE=0
DRIVER.CRC.VAR.HTU_MP1_ERRENA_1.VALUE=0
DRIVER.CRC.VAR.HTU_MP0_STADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPA_7_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPB_3_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPAL_3_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_CPA_4_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_CPB_0_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH2_BCTO.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_DCP0_MMADD_1.VALUE=POST_INCREMENT
DRIVER.CRC.VAR.HTU_ENAINTMAP_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH1_MODE_VALUE.VALUE=0x0001
DRIVER.CRC.VAR.HTU_ICPBL_0_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_CPB_5_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_MP1_ENA_1.VALUE=0
DRIVER.CRC.VAR.HTU_DCP0_HETADD.VALUE=0
DRIVER.CRC.VAR.HTU_ICPBL_5_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_3_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_CPA_0_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_RES_1.VALUE=0
DRIVER.CRC.VAR.HTU_DCP0_CPATMOD_1.VALUE=POST_INCREMENT
DRIVER.CRC.VAR.HTU_MP1_ENDADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPB_4_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_BASE.VALUE=0xFE000000
DRIVER.CRC.VAR.HTU_ICPAL_4_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.CRC_CH1_MODE.VALUE=FULL_CPU
DRIVER.CRC.VAR.HTU_CPA_5_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_CPB_1_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_VBHOLD_1.VALUE=0
DRIVER.CRC.VAR.HTU_MP0_ERRENA_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPBL_1_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_CPB_6_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.CRC_CH2_MODE.VALUE=FULL_CPU
DRIVER.CRC.VAR.HTU_DCP0_TRDAT_1.VALUE=32BIT
DRIVER.CRC.VAR.HTU_ICPBL_6_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_4_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPB_0_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPAL_0_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_CPA_1_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_DCP0_CPBTMOD_1.VALUE=POST_INCREMENT
DRIVER.CRC.VAR.CRC_CH1_PSIH.VALUE=0
DRIVER.CRC.VAR.HTU_MP0_ACC_1.VALUE=READ_ONLY
DRIVER.CRC.VAR.CRC_CH1_PSIL.VALUE=0
DRIVER.CRC.VAR.HTU_ICPB_5_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ICPAL_5_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_DCP0_ADMOD_1.VALUE=INCREMENT_16BIT
DRIVER.CRC.VAR.HTU_CPA_6_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_CPB_2_SEL_1.VALUE=ENABLE
DRIVER.CRC.VAR.HTU_ENA_1.VALUE=0
DRIVER.CRC.VAR.CRC_CH1_WDTO.VALUE=0x00000000
DRIVER.CRC.VAR.HTU_MP0_ENDADD_1.VALUE=0
DRIVER.CRC.VAR.HTU_ICPBL_2_SEL_1.VALUE=HIGH
DRIVER.CRC.VAR.HTU_ICPA_0_SEL_1.VALUE=ENABLE
DRIVER.EMAC.VAR.EMAC_ADD1.VALUE=FF
DRIVER.EMAC.VAR.EMAC_ADD2.VALUE=FF
DRIVER.EMAC.VAR.EMAC_ADD3.VALUE=FF
DRIVER.EMAC.VAR.EMAC_ADD4.VALUE=FF
DRIVER.EMAC.VAR.EMAC_ADD5.VALUE=FF
DRIVER.EMAC.VAR.EMAC_ADD6.VALUE=FF
DRIVER.EMAC.VAR.EMAC_CTRL_BASE.VALUE=0xFCF78800
DRIVER.EMAC.VAR.EMAC_LOOPBACK_ENA.VALUE=0
DRIVER.EMAC.VAR.MDIO_BASE.VALUE=0xFCF78900
DRIVER.EMAC.VAR.EMAC_BASE.VALUE=0xFCF78000
DRIVER.EMAC.VAR.EMAC_BASE_PORT.VALUE=0xFFFFFFFF
DRIVER.EMAC.VAR.EMAC_TRANSMIT_ENA.VALUE=1
DRIVER.EMAC.VAR.EMAC_CHANNELNUMBER.VALUE=0
DRIVER.EMAC.VAR.EMAC_RX_PBUF_ALLOC.VALUE=10
DRIVER.EMAC.VAR.EMAC_UNICAST_ENA.VALUE=1
DRIVER.EMAC.VAR.EMAC_FULL_DUPLEX_ENA.VALUE=1
DRIVER.EMAC.VAR.EMAC_PHYADDRESS.VALUE=1
DRIVER.EMAC.VAR.EMAC_MII_ENA.VALUE=1
DRIVER.EMAC.VAR.EMAC_CTRL_RAM_BASE.VALUE=0xFC520000
DRIVER.EMAC.VAR.EMAC_BROADCAST_ENA.VALUE=1
DRIVER.EMAC.VAR.EMAC_RECEIVE_ENA.VALUE=1
DRIVER.EMIF.VAR.EMIF_ASYNC2_TAVAV.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_TD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_EXTENDED_WAIT.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_TH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TA.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_WAIT.VALUE=pin0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_NOR_FLASH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TEHQZ.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_TA.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_TD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ENA_SDRAM.VALUE=1
DRIVER.EMIF.VAR.EMIF_ASYNC3_TH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TELQV.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_PAGE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_ENA.VALUE=1
DRIVER.EMIF.VAR.EMIF_SDRAM_REFRESH_CYCLES.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRC_MAX.VALUE=178
DRIVER.EMIF.VAR.EMIF_ASYNC3_TEHEL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_ENA.VALUE=1
DRIVER.EMIF.VAR.EMIF_ASYNC1_R_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_SDRAM_TRAS_MAX.VALUE=178
DRIVER.EMIF.VAR.EMIF_ASYNC3_TELEH.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRRD_MAX.VALUE=89
DRIVER.EMIF.VAR.EMIF_ASYNC3_STROBE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_ENA.VALUE=1
DRIVER.EMIF.VAR.EMIF_ASYNC1_ASIZE.VALUE=8_bit
DRIVER.EMIF.VAR.EMIF_SDRAM_TRC_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_TAVAV.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_BANKS.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_PAGE_DELAY_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRAS_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRRD_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_BASE.VALUE=0xFCFFE800
DRIVER.EMIF.VAR.EMIF_ASYNC3_TEHQZ.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_W_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_ASYNC3_TELQV.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TXSR_MAX.VALUE=356
DRIVER.EMIF.VAR.EMIF_ASYNC2_PAGE_SIZE.VALUE=4_words
DRIVER.EMIF.VAR.EMIF_CLKFRQ.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_W_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_ASYNC2_R_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_SDRAM_TREFRESH_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_R_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_SDRAM_TXSR_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_TSU.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_EXTENDED_WAIT.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_ASIZE.VALUE=8_bit
DRIVER.EMIF.VAR.EMIF_ASYNC2_TSU.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_W_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_ASYNC3_NOR_FLASH.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TWR_MAX.VALUE=89
DRIVER.EMIF.VAR.EMIF_ASYNC2_PAGE_DELAY_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_W_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_SDRAM_TREFRESH_DEFAULT.VALUE=2250
DRIVER.EMIF.VAR.EMIF_ASYNC3_TSU.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_PAGE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_STROBE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_R_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_SDRAM_TWR_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_R_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_SDRAM_TRCD_MAX.VALUE=89
DRIVER.EMIF.VAR.EMIF_CLK.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRCD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_W_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_SDRAM_TRFC.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_ASIZE.VALUE=8_bit
DRIVER.EMIF.VAR.EMIF_SDRAM_TRAS.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_NOR_FLASH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_PAGE_DELAY.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_CAS_LATENCY.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC_WAIT_POLARITY0.VALUE=pin_low
DRIVER.EMIF.VAR.EMIF_SDRAM_TRCD_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC_WAIT_POLARITY1.VALUE=pin_high
DRIVER.EMIF.VAR.EMIF_ASYNC1_PAGE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_R_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_SDRAM_TRC.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRRD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_PAGE_DELAY_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_W_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_ASYNC1_TEHEL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC_MAX_EXT_WAIT.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRP.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_PAGE_SIZE.VALUE=4_words
DRIVER.EMIF.VAR.EMIF_ASYNC1_W_SETUP.VALUE=15
DRIVER.EMIF.VAR.EMIF_ASYNC1_TELEH.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_REFRESH_CYCLES_MAX.VALUE=0
DRIVER.EMIF.VAR.EMIF_MS.VALUE=0.001
DRIVER.EMIF.VAR.EMIF_NS.VALUE=0.000000001
DRIVER.EMIF.VAR.EMIF_SDRAM_TWR.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_EXTENDED_WAIT.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_REFRESH_PERIOD.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC3_R_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_ASYNC1_TAVAV.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_PAGE_DELAY.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_WAIT.VALUE=pin0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRP_MAX.VALUE=89
DRIVER.EMIF.VAR.EMIF_SDRAM_TXSR.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_TEHQZ.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRFC_MAX.VALUE=356
DRIVER.EMIF.VAR.EMIF_ASYNC2_R_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_ASYNC1_TELQV.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_STROBE_MODE.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRP_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_REFRESH_PERIOD_MAX.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_W_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_ASYNC1_PAGE_SIZE.VALUE=4_words
DRIVER.EMIF.VAR.EMIF_ASYNC2_WAIT.VALUE=pin0
DRIVER.EMIF.VAR.EMIF_ASYNC2_TEHEL.VALUE=0
DRIVER.EMIF.VAR.EMIF_SDRAM_TRFC_VAL.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_R_HOLD.VALUE=7
DRIVER.EMIF.VAR.EMIF_SDRAM_PAGE_SIZE.VALUE=elements_256
DRIVER.EMIF.VAR.EMIF_ASYNC2_TELEH.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC1_PAGE_DELAY.VALUE=0
DRIVER.EMIF.VAR.EMIF_IBANK.VALUE=0
DRIVER.EMIF.VAR.EMIF_ASYNC2_W_STROBE.VALUE=63
DRIVER.EMIF.VAR.EMIF_ASYNC1_TA.VALUE=0
DRIVER.POM.VAR.POM_OVRLY_START_ADD28.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD29.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_10_ENA.VALUE=0
DRIVER.POM.VAR.POM_TIMEOUT_ENABLE.VALUE=0
DRIVER.POM.VAR.POM_REGION_11_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_20_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_12_ENA.VALUE=0
DRIVER.POM.VAR.POM_NO_OF_REGION.VALUE=1
DRIVER.POM.VAR.POM_REGION_21_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_13_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_30_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_22_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_14_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_31_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_23_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_15_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_1_ENA.VALUE=1
DRIVER.POM.VAR.POM_REGION_32_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_24_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_16_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_2_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_25_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_17_ENA.VALUE=0
DRIVER.POM.VAR.POM_OVRLY_START_ADD1.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD2.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD3.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD4.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD5.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD6.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD7.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD8.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVLY_TRG_REGION.VALUE=INTERNAL_RAM
DRIVER.POM.VAR.POM_OVRLY_START_ADD9.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_3_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_26_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_18_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_4_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_27_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_19_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_5_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_28_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_6_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_SIZE10.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE11.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE20.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE12.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_29_ENA.VALUE=0
DRIVER.POM.VAR.POM_REGION_SIZE21.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE13.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE30.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE22.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE14.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE31.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE23.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE15.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE32.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE24.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE16.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE25.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE17.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE26.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE18.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE27.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE19.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE28.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE29.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_7_ENA.VALUE=0
DRIVER.POM.VAR.POM_BASE.VALUE=0xFFA04000
DRIVER.POM.VAR.POM_PROG_START_ADD10.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD11.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD20.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD12.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD21.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD13.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_8_ENA.VALUE=0
DRIVER.POM.VAR.POM_PROG_START_ADD30.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD22.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD14.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD31.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD23.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD15.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD32.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD24.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD16.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD25.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD17.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD26.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD18.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD27.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD19.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_SIZE1.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_PROG_START_ADD28.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_SIZE2.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_PROG_START_ADD29.VALUE=0x00000000
DRIVER.POM.VAR.POM_REGION_SIZE3.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE4.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE5.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE6.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE7.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE8.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_SIZE9.VALUE=SIZE_64BYTES
DRIVER.POM.VAR.POM_REGION_9_ENA.VALUE=0
DRIVER.POM.VAR.POM_PROG_START_ADD1.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD2.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD3.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD4.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD5.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD6.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD7.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD8.VALUE=0x00000000
DRIVER.POM.VAR.POM_PROG_START_ADD9.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD10.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD11.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD20.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD12.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD21.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD13.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD30.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD22.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD14.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD31.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD23.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD15.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD32.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD24.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD16.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD25.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD17.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD26.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD18.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD27.VALUE=0x00000000
DRIVER.POM.VAR.POM_OVRLY_START_ADD19.VALUE=0x00000000
DRIVER.PMM.VAR.PMM_RAM_PWR_DOMAIN2_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_PWR_DOMAIN5_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_PWR_DOMAIN3_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_RAM_PWR_DOMAIN3_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_RAM_PWR_DOMAIN1_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_PWR_DOMAIN4_ENABLE.VALUE=0
DRIVER.PMM.VAR.PMM_PWR_DOMAIN2_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_START_SECTOR.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_4_BANK.VALUE=7
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_READ_CYCLE_COUNT.VALUE=10
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_NUMBER_OF_VIRTUAL_SECTORS.VALUE=4
DRIVER.FEE.VAR.FEE_BLOCK_INDEX15_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX4_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_FLASH_CRC_ENABLE.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_NUMBER.VALUE=12
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_5_BANK.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_NUMBER.VALUE=3
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_5_NUMBER.VALUE=5
DRIVER.FEE.VAR.FEE_SECTORS_EEP1.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_5_START.VALUE=4
DRIVER.FEE.VAR.FEE_BLOCK_NUMBER.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_DRIVER_INDEX.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_VS5_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX9_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX13_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX2_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_NUMBER.VALUE=10
DRIVER.FEE.VAR.FEE_NUMBER_OF_EEPS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_NUMBER.VALUE=8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_NUMBER.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_DEVICE_INDEX.VALUE=0
DRIVER.FEE.VAR.FEE_PAGE_OVERHEAD.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_3_NUMBER.VALUE=3
DRIVER.FEE.VAR.FEE_TI_FEE_SW_MAJOR_VERSION.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_1_END.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_1_START.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_SECTOR_NUMBER.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUALPAGE_SIZE.VALUE=8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_VS3_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX7_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_2_END.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX11_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_FLASH_WRITECOUNTER_SAVE.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_VS_INDEX.VALUE=2
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_NUMBER.VALUE=15
DRIVER.FEE.VAR.FEE_TI_FEE_SW_PATCH_VERSION.VALUE=0
DRIVER.FEE.VAR.FEE_JOBERROR_NOTIFICATION.VALUE=JobErrorNotification
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_NUMBER.VALUE=6
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_3_END.VALUE=2
DRIVER.FEE.VAR.FEE_BLOCK_SIZE.VALUE=0x10
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_TOTAL_BLOCKS_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_1_NUMBER.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_SIZE.VALUE=8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_4_END.VALUE=3
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_MAXIMUM_BLOCKING_TIME.VALUE=600
DRIVER.FEE.VAR.FEE_VS1_ENABLE.VALUE=1
DRIVER.FEE.VAR.FEE_NO_OF_UNCONFIGURED_BLOCKS_TO_COPY.VALUE=0
DRIVER.FEE.VAR.FEE_FLASH_BANK_NUM.VALUE=7
DRIVER.FEE.VAR.FEE_BLOCK_INDEX16_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX5_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_NUMBER.VALUE=13
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_5_END.VALUE=4
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_2_START.VALUE=1
DRIVER.FEE.VAR.FEE_SECTOR_OVERHEAD.VALUE=16
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_NUMBER.VALUE=4
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_TI_FEE_SW_MINOR_VERSION.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_VERSIONINFO_API.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_DATASETS.VALUE=1
DRIVER.FEE.VAR.MAX_BLOCK_TIME.VALUE=600
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_WRITE_CYCLES.VALUE=10
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_OFFSET.VALUE=16
DRIVER.FEE.VAR.FEE_NUMBER_OF_BLOCKS.VALUE=1
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_BANK.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX14_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX3_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_NUMBER_OF_EIGHTBYTEWRITES.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_3_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_11_NUMBER.VALUE=11
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_NUMBER.VALUE=9
DRIVER.FEE.VAR.FEE_END_SECTOR.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_1_BANK.VALUE=7
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_NUMBER.VALUE=2
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_FLASH_ERROR_CORRECTION_HANDLING.VALUE=TI_Fee_None
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_4_NUMBER.VALUE=4
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_DEVERROR_DETECT.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_3_START.VALUE=2
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_1_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_VS4_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX8_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_MAX_NUMBER_OF_LINKS.VALUE=256
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_EEP.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX12_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX1_ENABLE.VALUE=1
DRIVER.FEE.VAR.FEE_FLASH_ERROR_CORRECTION_ENABLE.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_2_BANK.VALUE=7
DRIVER.FEE.VAR.FEE_DATASELECT_BITS.VALUE=0
DRIVER.FEE.VAR.FEE_OPERATING_FREQ.VALUE=180
DRIVER.FEE.VAR.FEE_TOTAL_SECTORS.VALUE=4
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_16_NUMBER.VALUE=16
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_2_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_NUMBER.VALUE=7
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_9_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_2_NUMBER.VALUE=2
DRIVER.FEE.VAR.FEE_JOBEND_NOTIFICATION.VALUE=JobEndNotification
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_13_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_12_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_8_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_ENABLE_ECC.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_OVERHEAD.VALUE=24
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_7_DATASETS.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_4_OFFSET.VALUE=0
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_3_BANK.VALUE=7
DRIVER.FEE.VAR.FEE_VS2_ENABLE.VALUE=1
DRIVER.FEE.VAR.FEE_BLOCK_INDEX6_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_DEVICE_INDEX.VALUE=0x00000000
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_10_SIZE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX10_ENABLE.VALUE=0
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_14_NUMBER.VALUE=14
DRIVER.FEE.VAR.FEE_VIRTUAL_SECTOR_4_START.VALUE=3
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_15_IMED_DATA.VALUE=TRUE
DRIVER.FEE.VAR.FEE_CHECK_BANK7_ACCESS.VALUE=STD_OFF
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_WRITE_CYCLES.VALUE=0x8
DRIVER.FEE.VAR.FEE_POLLING_MODE.VALUE=STD_ON
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_5_NUMBER.VALUE=5
DRIVER.FEE.VAR.FEE_BLOCK_INDEX_6_SIZE.VALUE=0
