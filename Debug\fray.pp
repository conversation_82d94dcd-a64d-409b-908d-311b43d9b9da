# FIXED

fray.obj: ../fray.c
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/fray.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/fray_ram.h
fray.obj: ../LED_Show.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/gio.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h
fray.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h
fray.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h

../fray.c: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/fray.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/fray_ram.h: 
../LED_Show.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/gio.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_gio.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h: 
