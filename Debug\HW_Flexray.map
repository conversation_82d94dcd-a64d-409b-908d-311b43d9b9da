******************************************************************************
                  TI ARM Linker PC v5.2.2                      
******************************************************************************
>> Linked Wed Mar 11 10:52:58 2015

OUTPUT FILE NAME:   <HW_Flexray.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 00006434


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  VECTORS               00000000   00000020  00000020  00000000     X
  FLASH0                00000020   0017ffe0  000075f8  001789e8  R  X
  FLASH1                00180000   00180000  00000000  00180000  R  X
  STACKS                08000000   00001500  00000000  00001500  RW  
  RAM                   08001500   0003eb00  00000164  0003e99c  RW  


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007618   00007618    r-x
  00000000    00000000    00000020   00000020    r-x .intvecs
  00000020    00000020    00006fdc   00006fdc    r-x .text
  00006ffc    00006ffc    000005e4   000005e4    r-- .const
  000075e0    000075e0    00000038   00000038    r-- .cinit
08001500    08001500    00000164   00000000    rw-
  08001500    08001500    00000140   00000000    rw- .bss
  08001640    08001640    00000024   00000000    rw- .data


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    00000020     
                  00000000    00000020     sys_intvecs.obj (.intvecs)

.text      0    00000020    00006fdc     
                  00000020    00002db4     sys_selftest.obj (.text)
                  00002dd4    00000b20     system.obj (.text)
                  000038f4    00000984     FlexRay.obj (.text)
                  00004278    00000890     sys_vim.obj (.text)
                  00004b08    00000670     Fr.obj (.text)
                  00005178    00000634     esm.obj (.text)
                  000057ac    00000630     pinmux.obj (.text)
                  00005ddc    000003c4     sys_core.obj (.text)
                  000061a0    00000294     sys_vim.obj (.text:retain)
                  00006434    00000264     sys_startup.obj (.text:retain)
                  00006698    0000023c     LED_Show.obj (.text)
                  000068d4    00000158     esm.obj (.text:retain)
                  00006a2c    00000114     sys_pmu.obj (.text)
                  00006b40    000000e8     dabort.obj (.text)
                  00006c28    000000ac     notification.obj (.text)
                  00006cd4    0000009c     rtsv7R4_T_be_v3D16_eabi.lib : memcpy_t2.obj (.text)
                  00006d70    0000007a                                 : memset_t2.obj (.text)
                  00006dea    00000002     --HOLE-- [fill = 0]
                  00006dec    00000074     sys_main.obj (.text)
                  00006e60    0000006c     rtsv7R4_T_be_v3D16_eabi.lib : autoinit.obj (.text)
                  00006ecc    0000006a                                 : copy_decompress_rle.obj (.text)
                  00006f36    00000002     --HOLE-- [fill = 0]
                  00006f38    0000004c                                 : cpy_tbl.obj (.text)
                  00006f84    00000040                                 : exit.obj (.text)
                  00006fc4    00000014                                 : _lock.obj (.text)
                  00006fd8    0000000e                                 : copy_decompress_none.obj (.text:decompress:none)
                  00006fe6    0000000c                                 : copy_zero_init.obj (.text:decompress:ZI)
                  00006ff2    00000006                                 : copy_decompress_rle.obj (.text:decompress:rle24)
                  00006ff8    00000004     sys_phantom.obj (.text:retain)

.const     0    00006ffc    000005e4     
                  00006ffc    000002e8     FlexRay.obj (.const:trans_data)
                  000072e4    00000204     sys_vim.obj (.const:s_vim_init)
                  000074e8    000000f8     FlexRay.obj (.const:sync_data)

.cinit     0    000075e0    00000038     
                  000075e0    00000012     (.cinit..data.load) [load image, compression = rle]
                  000075f2    00000002     --HOLE-- [fill = 0]
                  000075f4    0000000c     (__TI_handler_table)
                  00007600    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00007608    00000010     (__TI_cinit_table)

.bss       0    08001500    00000140     UNINITIALIZED
                  08001500    00000080     sys_main.obj (.bss:active_node)
                  08001580    00000044     FlexRay.obj (.bss:Fr_Config)
                  080015c4    00000034     FlexRay.obj (.bss:Fr_LPdu)
                  080015f8    00000024     FlexRay.obj (.bss:Fr_LSdu1)
                  0800161c    00000024     FlexRay.obj (.bss:Fr_LSdu2)

.TI.noinit 
*          0    08001500    00000000     UNINITIALIZED

.data      0    08001640    00000024     UNINITIALIZED
                  08001640    0000000c     sys_main.obj (.data)
                  0800164c    00000008     LED_Show.obj (.data)
                  08001654    00000008     rtsv7R4_T_be_v3D16_eabi.lib : _lock.obj (.data)
                  0800165c    00000008                                 : exit.obj (.data)

.TI.persistent 
*          0    08001640    00000000     UNINITIALIZED


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00007608 records: 2, size/record: 8, table size: 16
	.data: load addr=000075e0, load size=00000012 bytes, run addr=08001640, run size=00000024 bytes, compression=rle
	.bss: load addr=00007600, load size=00000008 bytes, run addr=08001500, run size=00000140 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000075f4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_rle24
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                
-------   ----                                
00006f85  C$$EXIT                             
00003d08  FlexRay_Config                      
00004f94  Fr_AllowColdStart                   
08001580  Fr_Config                           
00004ed4  Fr_ControllerInit                   
00004c24  Fr_Init                             
080015c4  Fr_LPdu                             
080015f8  Fr_LSdu1                            
0800161c  Fr_LSdu2                            
00004b08  Fr_PrepareLPdu                      
00004e10  Fr_ReceiveRxLPdu                    
00004fe4  Fr_StartCommunication               
00004d48  Fr_TransmitTxLPdu                   
00006698  LED_Show                            
00003de4  Message_Buff_Config                 
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              
00007608  __TI_CINIT_Base                     
00007618  __TI_CINIT_Limit                    
000075f4  __TI_Handler_Table_Base             
00007600  __TI_Handler_Table_Limit            
00006198  __TI_PINIT_Base                     
0000619c  __TI_PINIT_Limit                    
00006e61  __TI_auto_init                      
0800165c  __TI_cleanup_ptr                    
00006fd9  __TI_decompress_none                
00006ff3  __TI_decompress_rle24               
08001660  __TI_dtors_ptr                      
00000000  __TI_static_base__                  
00006fe7  __TI_zero_init                      
00006d71  __aeabi_memclr                      
00006d71  __aeabi_memclr4                     
00006d71  __aeabi_memclr8                     
00006cd5  __aeabi_memcpy                      
00006cd5  __aeabi_memcpy4                     
00006cd5  __aeabi_memcpy8                     
00006d73  __aeabi_memset                      
00006d73  __aeabi_memset4                     
00006d73  __aeabi_memset8                     
ffffffff  __binit__                           
ffffffff  __c_args__                          
00006434  _c_int00                            
00006090  _coreClearAuxiliaryDataFault_       
000060ac  _coreClearAuxiliaryInstructionFault_
00006058  _coreClearDataFaultAddress_         
00006020  _coreClearDataFault_                
00006074  _coreClearInstructionFaultAddress_  
0000603c  _coreClearInstructionFault_         
00005f84  _coreDisableEventBusExport_         
00005fe8  _coreDisableFlashEcc_               
00005fb4  _coreDisableRamEcc_                 
00005f6c  _coreEnableEventBusExport_          
00005fcc  _coreEnableFlashEcc_                
00006000  _coreEnableIrqVicOffset_            
00005f9c  _coreEnableRamEcc_                  
00005f54  _coreEnableVfp_                     
00006088  _coreGetAuxiliaryDataFault_         
000060a4  _coreGetAuxiliaryInstructionFault_  
00006050  _coreGetDataFaultAddress_           
00006018  _coreGetDataFault_                  
0000606c  _coreGetInstructionFaultAddress_    
00006034  _coreGetInstructionFault_           
00005ddc  _coreInitRegisters_                 
00005ee8  _coreInitStackPointer_              
00006b40  _dabort                             
000060c8  _disable_FIQ_interrupt_             
000060d0  _disable_IRQ_interrupt_             
000060c0  _disable_interrupt_                 
000060d8  _enable_interrupt_                  
00006168  _errata_CORTEXR4_57_                
00006180  _errata_CORTEXR4_66_                
000060e0  _esmCcmErrorsClear_                 
00005f34  _getCPSRValue_                      
00005f3c  _gotoCPUIdle_                       
08001654  _lock                               
00006fd3  _nop                                
00006a94  _pmuDisableCountersGlobal_          
00006a7c  _pmuEnableCountersGlobal_           
00006b14  _pmuGetCycleCount_                  
00006b1c  _pmuGetEventCount_                  
00006b2c  _pmuGetOverflow_                    
00006a2c  _pmuInit_                           
00006adc  _pmuResetCounters_                  
00006aac  _pmuResetCycleCounter_              
00006ac4  _pmuResetEventCounters_             
00006b04  _pmuSetCountEvent_                  
00006af4  _pmuStartCounters_                  
00006afc  _pmuStopCounters_                   
00006fcb  _register_lock                      
00006fc5  _register_unlock                    
08001658  _unlock                             
00006f89  abort                               
08001500  active_node                         
000012e8  adc1ParityCheck                     
000013d8  adc2ParityCheck                     
ffffffff  binit                               
00001480  can1ParityCheck                     
00001558  can2ParityCheck                     
00001620  can3ParityCheck                     
00000030  ccmSelfCheck                        
000027d8  ccmr4GetConfigValue                 
000009a4  checkB0RAMECC                       
00000b94  checkB1RAMECC                       
00001c88  checkClockMonitor                   
00000d84  checkFlashECC                       
00001d64  checkFlashEEPROMECC                 
00001efc  checkPLL1Slip                       
00001ff8  checkPLL2Slip                       
000020c0  checkRAMAddrParity                  
00001a40  checkRAMECC                         
000021e0  checkRAMUERRTest                    
00000828  checkefcSelfTest                    
000041b0  clear_WRDS                          
000038f4  configure_initialize_node           
00006f39  copy_in                             
00000234  cpuSelfTest                         
00000e68  cpuSelfTestFail                     
00000e60  custom_dabort                       
00002c34  disableParity                       
00006cc0  dmaGroupANotification               
00000f18  dmaParityCheck                      
00006c98  edgeNotification                    
000006cc  efcCheck                            
00002740  efcGetConfigValue                   
00000800  efcSelfTest                         
00000754  efcStuckZeroTest                    
00002ba8  enableParity                        
00002844  errata_PBIST_4                      
08001640  error                               
00005328  esmActivateNormalOperation          
00005414  esmClearStatus                      
00005454  esmClearStatusBuffer                
000052ec  esmDisableError                     
00005364  esmDisableInterrupt                 
000052c0  esmEnableError                      
00005338  esmEnableInterrupt                  
00005574  esmEnterSelfTest                    
000052a4  esmError                            
0000564c  esmGetConfigValue                   
000054a0  esmGetStatus                        
00005514  esmGetStatusBuffer                  
00006c28  esmGroup1Notification               
00006c38  esmGroup2Notification               
000068d4  esmHighInterrupt                    
00005178  esmInit                             
000055fc  esmSelfTestStatus                   
00005480  esmSetCounterPreloadValue           
00005390  esmSetInterruptLevel                
00005318  esmTriggerErrorPinReset             
00006f91  exit                                
00002344  fmcBus1ParityCheck                  
000008c8  fmcBus2Check                        
00000904  fmcECCcheck                         
00005024  header_crc_calc                     
00000fd0  het1ParityCheck                     
00001144  het2ParityCheck                     
00006cac  hetNotification                     
00001080  htu1ParityCheck                     
00001228  htu2ParityCheck                     
08001644  led_show                            
00006dec  main                                
00002f10  mapClocks                           
00006cd5  memcpy                              
00000174  memoryInit                          
00006c48  memoryPort0TestFailNotification     
00006c64  memoryPort1TestFailNotification     
00006d79  memset                              
000016f4  mibspi1ParityCheck                  
000017fc  mibspi3ParityCheck                  
00001920  mibspi5ParityCheck                  
000057ac  muxInit                             
08001648  node_num                            
00002470  pbistFail                           
00002558  pbistGetConfigValue                 
000005d0  pbistIsTestCompleted                
000005fc  pbistIsTestPassed                   
00000650  pbistPortTestStatus                 
000004d4  pbistRun                            
000002cc  pbistSelfCheck                      
000005a0  pbistStop                           
00002ebc  periphInit                          
00006ff8  phantomInterrupt                    
00005b30  pinmuxGetConfigValue                
00006c80  pwmNotification                     
00000000  resetEntry                          
00000020  selftestFailNotification            
00002e7c  setupFlash                          
00002dd4  setupPLL                            
000036d8  sramGetConfigValue                  
000026a0  stcGetConfigValue                   
000001b8  stcSelfCheck                        
00000e64  stcSelfCheckFail                    
000074e8  sync_data                           
00003118  systemGetConfigValue                
0000301c  systemInit                          
000030e8  systemPowerDown                     
0000352c  tcmflashGetConfigValue              
00006ffc  trans_data                          
000039b4  transmit_check_node                 
00002e34  trimLPO                             
0000434c  vimChannelMap                       
00004540  vimDisableInterrupt                 
000043e8  vimEnableInterrupt                  
000045c4  vimGetConfigValue                   
00004278  vimInit                             
00000e6c  vimParityCheck                      
000061a0  vimParityErrorHandler               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                
-------   ----                                
00000000  __TI_static_base__                  
00000000  resetEntry                          
00000020  selftestFailNotification            
00000030  ccmSelfCheck                        
00000174  memoryInit                          
000001b8  stcSelfCheck                        
00000234  cpuSelfTest                         
000002cc  pbistSelfCheck                      
000004d4  pbistRun                            
000005a0  pbistStop                           
000005d0  pbistIsTestCompleted                
000005fc  pbistIsTestPassed                   
00000650  pbistPortTestStatus                 
000006cc  efcCheck                            
00000754  efcStuckZeroTest                    
00000800  efcSelfTest                         
00000828  checkefcSelfTest                    
000008c8  fmcBus2Check                        
00000904  fmcECCcheck                         
000009a4  checkB0RAMECC                       
00000b94  checkB1RAMECC                       
00000d84  checkFlashECC                       
00000e60  custom_dabort                       
00000e64  stcSelfCheckFail                    
00000e68  cpuSelfTestFail                     
00000e6c  vimParityCheck                      
00000f18  dmaParityCheck                      
00000fd0  het1ParityCheck                     
00001080  htu1ParityCheck                     
00001144  het2ParityCheck                     
00001228  htu2ParityCheck                     
000012e8  adc1ParityCheck                     
000013d8  adc2ParityCheck                     
00001480  can1ParityCheck                     
00001558  can2ParityCheck                     
00001620  can3ParityCheck                     
000016f4  mibspi1ParityCheck                  
000017fc  mibspi3ParityCheck                  
00001920  mibspi5ParityCheck                  
00001a40  checkRAMECC                         
00001c88  checkClockMonitor                   
00001d64  checkFlashEEPROMECC                 
00001efc  checkPLL1Slip                       
00001ff8  checkPLL2Slip                       
000020c0  checkRAMAddrParity                  
000021e0  checkRAMUERRTest                    
00002344  fmcBus1ParityCheck                  
00002470  pbistFail                           
00002558  pbistGetConfigValue                 
000026a0  stcGetConfigValue                   
00002740  efcGetConfigValue                   
000027d8  ccmr4GetConfigValue                 
00002844  errata_PBIST_4                      
00002ba8  enableParity                        
00002c34  disableParity                       
00002dd4  setupPLL                            
00002e34  trimLPO                             
00002e7c  setupFlash                          
00002ebc  periphInit                          
00002f10  mapClocks                           
0000301c  systemInit                          
000030e8  systemPowerDown                     
00003118  systemGetConfigValue                
0000352c  tcmflashGetConfigValue              
000036d8  sramGetConfigValue                  
000038f4  configure_initialize_node           
000039b4  transmit_check_node                 
00003d08  FlexRay_Config                      
00003de4  Message_Buff_Config                 
000041b0  clear_WRDS                          
00004278  vimInit                             
0000434c  vimChannelMap                       
000043e8  vimEnableInterrupt                  
00004540  vimDisableInterrupt                 
000045c4  vimGetConfigValue                   
00004b08  Fr_PrepareLPdu                      
00004c24  Fr_Init                             
00004d48  Fr_TransmitTxLPdu                   
00004e10  Fr_ReceiveRxLPdu                    
00004ed4  Fr_ControllerInit                   
00004f94  Fr_AllowColdStart                   
00004fe4  Fr_StartCommunication               
00005024  header_crc_calc                     
00005178  esmInit                             
000052a4  esmError                            
000052c0  esmEnableError                      
000052ec  esmDisableError                     
00005318  esmTriggerErrorPinReset             
00005328  esmActivateNormalOperation          
00005338  esmEnableInterrupt                  
00005364  esmDisableInterrupt                 
00005390  esmSetInterruptLevel                
00005414  esmClearStatus                      
00005454  esmClearStatusBuffer                
00005480  esmSetCounterPreloadValue           
000054a0  esmGetStatus                        
00005514  esmGetStatusBuffer                  
00005574  esmEnterSelfTest                    
000055fc  esmSelfTestStatus                   
0000564c  esmGetConfigValue                   
000057ac  muxInit                             
00005b30  pinmuxGetConfigValue                
00005ddc  _coreInitRegisters_                 
00005ee8  _coreInitStackPointer_              
00005f34  _getCPSRValue_                      
00005f3c  _gotoCPUIdle_                       
00005f54  _coreEnableVfp_                     
00005f6c  _coreEnableEventBusExport_          
00005f84  _coreDisableEventBusExport_         
00005f9c  _coreEnableRamEcc_                  
00005fb4  _coreDisableRamEcc_                 
00005fcc  _coreEnableFlashEcc_                
00005fe8  _coreDisableFlashEcc_               
00006000  _coreEnableIrqVicOffset_            
00006018  _coreGetDataFault_                  
00006020  _coreClearDataFault_                
00006034  _coreGetInstructionFault_           
0000603c  _coreClearInstructionFault_         
00006050  _coreGetDataFaultAddress_           
00006058  _coreClearDataFaultAddress_         
0000606c  _coreGetInstructionFaultAddress_    
00006074  _coreClearInstructionFaultAddress_  
00006088  _coreGetAuxiliaryDataFault_         
00006090  _coreClearAuxiliaryDataFault_       
000060a4  _coreGetAuxiliaryInstructionFault_  
000060ac  _coreClearAuxiliaryInstructionFault_
000060c0  _disable_interrupt_                 
000060c8  _disable_FIQ_interrupt_             
000060d0  _disable_IRQ_interrupt_             
000060d8  _enable_interrupt_                  
000060e0  _esmCcmErrorsClear_                 
00006168  _errata_CORTEXR4_57_                
00006180  _errata_CORTEXR4_66_                
00006198  __TI_PINIT_Base                     
0000619c  __TI_PINIT_Limit                    
000061a0  vimParityErrorHandler               
00006434  _c_int00                            
00006698  LED_Show                            
000068d4  esmHighInterrupt                    
00006a2c  _pmuInit_                           
00006a7c  _pmuEnableCountersGlobal_           
00006a94  _pmuDisableCountersGlobal_          
00006aac  _pmuResetCycleCounter_              
00006ac4  _pmuResetEventCounters_             
00006adc  _pmuResetCounters_                  
00006af4  _pmuStartCounters_                  
00006afc  _pmuStopCounters_                   
00006b04  _pmuSetCountEvent_                  
00006b14  _pmuGetCycleCount_                  
00006b1c  _pmuGetEventCount_                  
00006b2c  _pmuGetOverflow_                    
00006b40  _dabort                             
00006c28  esmGroup1Notification               
00006c38  esmGroup2Notification               
00006c48  memoryPort0TestFailNotification     
00006c64  memoryPort1TestFailNotification     
00006c80  pwmNotification                     
00006c98  edgeNotification                    
00006cac  hetNotification                     
00006cc0  dmaGroupANotification               
00006cd5  __aeabi_memcpy                      
00006cd5  __aeabi_memcpy4                     
00006cd5  __aeabi_memcpy8                     
00006cd5  memcpy                              
00006d71  __aeabi_memclr                      
00006d71  __aeabi_memclr4                     
00006d71  __aeabi_memclr8                     
00006d73  __aeabi_memset                      
00006d73  __aeabi_memset4                     
00006d73  __aeabi_memset8                     
00006d79  memset                              
00006dec  main                                
00006e61  __TI_auto_init                      
00006f39  copy_in                             
00006f85  C$$EXIT                             
00006f89  abort                               
00006f91  exit                                
00006fc5  _register_unlock                    
00006fcb  _register_lock                      
00006fd3  _nop                                
00006fd9  __TI_decompress_none                
00006fe7  __TI_zero_init                      
00006ff3  __TI_decompress_rle24               
00006ff8  phantomInterrupt                    
00006ffc  trans_data                          
000074e8  sync_data                           
000075f4  __TI_Handler_Table_Base             
00007600  __TI_Handler_Table_Limit            
00007608  __TI_CINIT_Base                     
00007618  __TI_CINIT_Limit                    
08001500  active_node                         
08001580  Fr_Config                           
080015c4  Fr_LPdu                             
080015f8  Fr_LSdu1                            
0800161c  Fr_LSdu2                            
08001640  error                               
08001644  led_show                            
08001648  node_num                            
08001654  _lock                               
08001658  _unlock                             
0800165c  __TI_cleanup_ptr                    
08001660  __TI_dtors_ptr                      
ffffffff  __binit__                           
ffffffff  __c_args__                          
ffffffff  binit                               
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              

[207 symbols]
