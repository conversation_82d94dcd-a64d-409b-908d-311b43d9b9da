<?xml version="1.0" encoding="UTF-8"?>
<SETTINGS>
  <DEVICE>
    <family>TMS570LS31x</family>
    <device>TMS570LS3137ZWT</device>
    <dilfile>HW_Flexray.dil</dilfile>
    <tools>ti</tools>
  </DEVICE>
  <VERSION>
    <version>03.08.01</version>
  </VERSION>
  <HET1>
    <FILENAME>
      <HET1ASMHDR>
        <NAME/>
      </HET1ASMHDR>
      <HET1ASMSRC>
        <NAME/>
      </HET1ASMSRC>
    </FILENAME>
  </HET1>
  <HET2>
    <FILENAME>
      <HET2ASMHDR>
        <NAME/>
      </HET2ASMHDR>
      <HET2ASMSRC>
        <NAME/>
      </HET2ASMSRC>
    </FILENAME>
  </HET2>
  <SYSTEM>
    <FILENAMES>
      <HDRHAL>
        <NAME>hal_stdtypes.h</NAME>
        <PATH>include\hal_stdtypes.h</PATH>
      </HDRHAL>
      <HDRCOMMON>
        <NAME>sys_common.h</NAME>
        <PATH>include\sys_common.h</PATH>
      </HDRCOMMON>
      <HDRSYSTEM_R>
        <NAME>reg_system.h</NAME>
        <PATH>include\reg_system.h</PATH>
      </HDRSYSTEM_R>
      <HDRFLASH_R>
        <NAME>reg_flash.h</NAME>
        <PATH>include\reg_flash.h</PATH>
      </HDRFLASH_R>
      <HDRTCRAM_R>
        <NAME>reg_tcram.h</NAME>
        <PATH>include\reg_tcram.h</PATH>
      </HDRTCRAM_R>
      <HDRVIM_R>
        <NAME>reg_vim.h</NAME>
        <PATH>include\reg_vim.h</PATH>
      </HDRVIM_R>
      <HDRPBIST_R>
        <NAME>reg_pbist.h</NAME>
        <PATH>include\reg_pbist.h</PATH>
      </HDRPBIST_R>
      <HDRSTC_R>
        <NAME>reg_stc.h</NAME>
        <PATH>include\reg_stc.h</PATH>
      </HDRSTC_R>
      <HDREFC_R>
        <NAME>reg_efc.h</NAME>
        <PATH>include\reg_efc.h</PATH>
      </HDREFC_R>
      <HDRPCR_R>
        <NAME>reg_pcr.h</NAME>
        <PATH>include\reg_pcr.h</PATH>
      </HDRPCR_R>
      <HDRPMM_R>
        <NAME>reg_pmm.h</NAME>
        <PATH>include\reg_pmm.h</PATH>
      </HDRPMM_R>
      <HDRDMA_R>
        <NAME>reg_dma.h</NAME>
        <PATH>include\reg_dma.h</PATH>
      </HDRDMA_R>
      <HDRSYSTEM>
        <NAME>system.h</NAME>
        <PATH>include\system.h</PATH>
      </HDRSYSTEM>
      <HDRVIM>
        <NAME>sys_vim.h</NAME>
        <PATH>include\sys_vim.h</PATH>
      </HDRVIM>
      <HDRCORE>
        <NAME>sys_core.h</NAME>
        <PATH>include\sys_core.h</PATH>
      </HDRCORE>
      <HDRMPU>
        <NAME>sys_mpu.h</NAME>
        <PATH>include\sys_mpu.h</PATH>
      </HDRMPU>
      <HDRPMU>
        <NAME>sys_pmu.h</NAME>
        <PATH>include\sys_pmu.h</PATH>
      </HDRPMU>
      <HDRPCR>
        <NAME>sys_pcr.h</NAME>
        <PATH>include\sys_pcr.h</PATH>
      </HDRPCR>
      <HDRPMM>
        <NAME>sys_pmm.h</NAME>
        <PATH>include\sys_pmm.h</PATH>
      </HDRPMM>
      <HDRDMA>
        <NAME>sys_dma.h</NAME>
        <PATH>include\sys_dma.h</PATH>
      </HDRDMA>
      <HDRSLF>
        <NAME>sys_selftest.h</NAME>
        <PATH>include\sys_selftest.h</PATH>
      </HDRSLF>
      <SRCCORE>
        <NAME>sys_core.asm</NAME>
        <PATH>source\sys_core.asm</PATH>
      </SRCCORE>
      <SRCINTVECS>
        <NAME>sys_intvecs.asm</NAME>
        <PATH>source\sys_intvecs.asm</PATH>
      </SRCINTVECS>
      <SRCMPU>
        <NAME>sys_mpu.asm</NAME>
        <PATH>source\sys_mpu.asm</PATH>
      </SRCMPU>
      <SRCPMU>
        <NAME>sys_pmu.asm</NAME>
        <PATH>source\sys_pmu.asm</PATH>
      </SRCPMU>
      <SRCDABORT>
        <NAME>dabort.asm</NAME>
        <PATH>source\dabort.asm</PATH>
      </SRCDABORT>
      <SRCPCR>
        <NAME>sys_pcr.c</NAME>
        <PATH>source\sys_pcr.c</PATH>
      </SRCPCR>
      <SRCPMM>
        <NAME>sys_pmm.c</NAME>
        <PATH>source\sys_pmm.c</PATH>
      </SRCPMM>
      <SRCDMA>
        <NAME>sys_dma.c</NAME>
        <PATH>source\sys_dma.c</PATH>
      </SRCDMA>
      <SRCSYSTEM>
        <NAME>system.c</NAME>
        <PATH>source\system.c</PATH>
      </SRCSYSTEM>
      <SRCPHANTOM>
        <NAME>sys_phantom.c</NAME>
        <PATH>source\sys_phantom.c</PATH>
      </SRCPHANTOM>
      <SRCSTARTUP>
        <NAME>sys_startup.c</NAME>
        <PATH>source\sys_startup.c</PATH>
      </SRCSTARTUP>
      <SRCSLF>
        <NAME>sys_selftest.c</NAME>
        <PATH>source\sys_selftest.c</PATH>
      </SRCSLF>
      <SRCVIM>
        <NAME>sys_vim.c</NAME>
        <PATH>source\sys_vim.c</PATH>
      </SRCVIM>
      <SRCMAIN>
        <NAME>sys_main.c</NAME>
        <PATH>source\sys_main.c</PATH>
      </SRCMAIN>
      <SRCNOTIFY>
        <NAME>notification.c</NAME>
        <PATH>source\notification.c</PATH>
      </SRCNOTIFY>
      <SRCCMD>
        <NAME>sys_link.cmd</NAME>
        <PATH>source\sys_link.cmd</PATH>
      </SRCCMD>
      <HDRTEST>
        <NAME>HL_Test.h</NAME>
      </HDRTEST>
      <HDRMUX_R>
        <NAME>reg_pinmux.h</NAME>
      </HDRMUX_R>
      <HDRMUX>
        <NAME>pinmux.h</NAME>
      </HDRMUX>
      <SRCMUX>
        <NAME>pinmux.c</NAME>
      </SRCMUX>
      <HDRRTI_R>
        <NAME>reg_rti.h</NAME>
      </HDRRTI_R>
      <HDRRTI>
        <NAME>rti.h</NAME>
      </HDRRTI>
      <SRCRTI/>
      <HDRGIO_R>
        <NAME>reg_gio.h</NAME>
      </HDRGIO_R>
      <HDRGIO>
        <NAME>gio.h</NAME>
      </HDRGIO>
      <SRCGIO/>
      <HDRSCI_R>
        <NAME>reg_sci.h</NAME>
      </HDRSCI_R>
      <HDRSCI>
        <NAME>sci.h</NAME>
      </HDRSCI>
      <SRCSCI/>
      <HDRLIN_R>
        <NAME>reg_lin.h</NAME>
      </HDRLIN_R>
      <HDRLIN>
        <NAME>lin.h</NAME>
      </HDRLIN>
      <SRCLIN/>
      <HDRMIBSPI_R>
        <NAME>reg_mibspi.h</NAME>
      </HDRMIBSPI_R>
      <HDRMIBSPI>
        <NAME>mibspi.h</NAME>
      </HDRMIBSPI>
      <SRCMIBSPI/>
      <HDRSPI_R>
        <NAME>reg_spi.h</NAME>
      </HDRSPI_R>
      <HDRSPI>
        <NAME>spi.h</NAME>
      </HDRSPI>
      <SRCSPI/>
      <HDRCAN_R>
        <NAME>reg_can.h</NAME>
      </HDRCAN_R>
      <HDRCAN>
        <NAME>can.h</NAME>
      </HDRCAN>
      <SRCCAN/>
      <HDRADC_R>
        <NAME>reg_adc.h</NAME>
      </HDRADC_R>
      <HDRADC>
        <NAME>adc.h</NAME>
      </HDRADC>
      <SRCADC/>
      <HET1ASMHDR>
        <NAME/>
      </HET1ASMHDR>
      <HET1ASMSRC>
        <NAME/>
      </HET1ASMSRC>
      <STDHDRHET>
        <NAME>std_nhet.h</NAME>
      </STDHDRHET>
      <HDRHET_R>
        <NAME>reg_het.h</NAME>
      </HDRHET_R>
      <HDRHET>
        <NAME>het.h</NAME>
      </HDRHET>
      <HDRHTU_R>
        <NAME>reg_htu.h</NAME>
      </HDRHTU_R>
      <HDRHTU>
        <NAME>htu.h</NAME>
      </HDRHTU>
      <SRCHET>
        <NAME>het.c</NAME>
      </SRCHET>
      <HET2ASMHDR>
        <NAME/>
      </HET2ASMHDR>
      <HET2ASMSRC>
        <NAME/>
      </HET2ASMSRC>
      <HDRESM_R>
        <NAME>reg_esm.h</NAME>
      </HDRESM_R>
      <HDRESM>
        <NAME>esm.h</NAME>
      </HDRESM>
      <SRCESM>
        <NAME>esm.c</NAME>
      </SRCESM>
      <HDRI2C_R>
        <NAME>reg_i2c.h</NAME>
      </HDRI2C_R>
      <HDRI2C>
        <NAME>i2c.h</NAME>
      </HDRI2C>
      <SRCI2C/>
      <HDR1EMAC>
        <NAME>emac.h</NAME>
      </HDR1EMAC>
      <HDR2EMAC>
        <NAME>hw_emac.h</NAME>
      </HDR2EMAC>
      <HDR3EMAC>
        <NAME>hw_emac_ctrl.h</NAME>
      </HDR3EMAC>
      <HDR4EMAC>
        <NAME>hw_mdio.h</NAME>
      </HDR4EMAC>
      <HDR5EMAC>
        <NAME>hw_reg_access.h</NAME>
      </HDR5EMAC>
      <HDR6EMAC>
        <NAME>mdio.h</NAME>
      </HDR6EMAC>
      <HDRPHY>
        <NAME>phy_dp83640.h</NAME>
      </HDRPHY>
      <SRC1EMAC/>
      <SRC2EMAC/>
      <SRCPHY/>
      <HDRDCC_R>
        <NAME>reg_dcc.h</NAME>
      </HDRDCC_R>
      <HDRDCC>
        <NAME>dcc.h</NAME>
      </HDRDCC>
      <SRCDCC/>
      <HDRRTP_R>
        <NAME>reg_rtp.h</NAME>
      </HDRRTP_R>
      <HDRRTP>
        <NAME>rtp.h</NAME>
      </HDRRTP>
      <SRCRTP/>
      <HDRDMM_R>
        <NAME>reg_dmm.h</NAME>
      </HDRDMM_R>
      <HDRDMM>
        <NAME>dmm.h</NAME>
      </HDRDMM>
      <SRCDMM/>
      <HDREMIF_R>
        <NAME>reg_emif.h</NAME>
      </HDREMIF_R>
      <HDREMIF>
        <NAME>emif.h</NAME>
      </HDREMIF>
      <SRCEMIF/>
      <HDRPOM_R>
        <NAME>reg_pom.h</NAME>
      </HDRPOM_R>
      <HDRPOM>
        <NAME>pom.h</NAME>
      </HDRPOM>
      <SRCPOM/>
      <HDRCRC_R>
        <NAME>reg_crc.h</NAME>
      </HDRCRC_R>
      <HDRCRC>
        <NAME>crc.h</NAME>
      </HDRCRC>
      <SRCCRC/>
      <HDR1FEE>
        <NAME>Device_TMS570LS31.h</NAME>
      </HDR1FEE>
      <HDR2FEE>
        <NAME>Device_header.h</NAME>
      </HDR2FEE>
      <HDR3FEE>
        <NAME>Device_types.h</NAME>
      </HDR3FEE>
      <HDR4FEE>
        <NAME>ti_fee_Cfg.h</NAME>
      </HDR4FEE>
      <HDR5FEE>
        <NAME>MemMap.h</NAME>
      </HDR5FEE>
      <HDR6FEE>
        <NAME>ti_fee_types.h</NAME>
      </HDR6FEE>
      <HDR7FEE>
        <NAME>ti_fee.h</NAME>
      </HDR7FEE>
      <HDR8FEE>
        <NAME>fee_interface.h</NAME>
      </HDR8FEE>
      <SRC1FEE/>
      <SRC2FEE/>
      <SRC3FEE/>
      <SRC4FEE/>
      <SRC5FEE/>
      <SRC6FEE/>
      <SRC7FEE/>
      <SRC8FEE/>
      <SRC9FEE/>
      <SRC10FEE/>
      <SRC11FEE/>
      <SRC12FEE/>
      <SRC13FEE/>
      <SRC14FEE/>
      <SRC15FEE/>
      <SRC16FEE/>
    </FILENAMES>
  </SYSTEM>
  <PINMUX>
    <FILENAMES>
      <HDRMUX_R>
        <PATH>include\reg_pinmux.h</PATH>
      </HDRMUX_R>
      <HDRMUX>
        <PATH>include\pinmux.h</PATH>
      </HDRMUX>
      <SRCMUX>
        <PATH>source\pinmux.c</PATH>
      </SRCMUX>
    </FILENAMES>
  </PINMUX>
  <RTI>
    <FILENAMES>
      <HDRRTI_R>
        <PATH>include\reg_rti.h</PATH>
      </HDRRTI_R>
      <HDRRTI>
        <PATH>include\rti.h</PATH>
      </HDRRTI>
      <SRCRTI>
        <PATH/>
      </SRCRTI>
    </FILENAMES>
  </RTI>
  <GIO>
    <FILENAMES>
      <HDRGIO_R>
        <PATH>include\reg_gio.h</PATH>
      </HDRGIO_R>
      <HDRGIO>
        <PATH>include\gio.h</PATH>
      </HDRGIO>
      <SRCGIO>
        <PATH/>
      </SRCGIO>
    </FILENAMES>
  </GIO>
  <SCI>
    <FILENAMES>
      <HDRSCI_R>
        <PATH>include\reg_sci.h</PATH>
      </HDRSCI_R>
      <HDRSCI>
        <PATH>include\sci.h</PATH>
      </HDRSCI>
      <SRCSCI>
        <PATH/>
      </SRCSCI>
    </FILENAMES>
  </SCI>
  <LIN>
    <FILENAMES>
      <HDRLIN_R>
        <PATH>include\reg_lin.h</PATH>
      </HDRLIN_R>
      <HDRLIN>
        <PATH>include\lin.h</PATH>
      </HDRLIN>
      <SRCLIN>
        <PATH/>
      </SRCLIN>
    </FILENAMES>
  </LIN>
  <MIBSPI>
    <FILENAMES>
      <HDRMIBSPI_R>
        <PATH>include\reg_mibspi.h</PATH>
      </HDRMIBSPI_R>
      <HDRMIBSPI>
        <PATH>include\mibspi.h</PATH>
      </HDRMIBSPI>
      <SRCMIBSPI>
        <PATH/>
      </SRCMIBSPI>
    </FILENAMES>
  </MIBSPI>
  <SPI>
    <FILENAMES>
      <HDRSPI_R>
        <PATH>include\reg_spi.h</PATH>
      </HDRSPI_R>
      <HDRSPI>
        <PATH>include\spi.h</PATH>
      </HDRSPI>
      <SRCSPI>
        <PATH/>
      </SRCSPI>
    </FILENAMES>
  </SPI>
  <CAN>
    <FILENAMES>
      <HDRCAN_R>
        <PATH>include\reg_can.h</PATH>
      </HDRCAN_R>
      <HDRCAN>
        <PATH>include\can.h</PATH>
      </HDRCAN>
      <SRCCAN>
        <PATH/>
      </SRCCAN>
    </FILENAMES>
  </CAN>
  <ADC>
    <FILENAMES>
      <HDRADC_R>
        <PATH>include\reg_adc.h</PATH>
      </HDRADC_R>
      <HDRADC>
        <PATH>include\adc.h</PATH>
      </HDRADC>
      <SRCADC>
        <PATH/>
      </SRCADC>
    </FILENAMES>
  </ADC>
  <HET>
    <FILENAMES>
      <STDHDRHET>
        <PATH>include\std_nhet.h</PATH>
      </STDHDRHET>
      <HDRHET_R>
        <PATH>include\reg_het.h</PATH>
      </HDRHET_R>
      <HDRHET>
        <PATH>include\het.h</PATH>
      </HDRHET>
      <HDRHTU_R>
        <PATH>include\reg_htu.h</PATH>
      </HDRHTU_R>
      <HDRHTU>
        <PATH>include\htu.h</PATH>
      </HDRHTU>
      <SRCHET>
        <PATH>source\het.c</PATH>
      </SRCHET>
    </FILENAMES>
  </HET>
  <ESM>
    <FILENAMES>
      <HDRESM_R>
        <PATH>include\reg_esm.h</PATH>
      </HDRESM_R>
      <HDRESM>
        <PATH>include\esm.h</PATH>
      </HDRESM>
      <SRCESM>
        <PATH>source\esm.c</PATH>
      </SRCESM>
    </FILENAMES>
  </ESM>
  <I2C>
    <FILENAMES>
      <HDRI2C_R>
        <PATH>include\reg_i2c.h</PATH>
      </HDRI2C_R>
      <HDRI2C>
        <PATH>include\i2c.h</PATH>
      </HDRI2C>
      <SRCI2C>
        <PATH/>
      </SRCI2C>
    </FILENAMES>
  </I2C>
  <EMAC>
    <FILENAMES>
      <HDR1EMAC>
        <PATH>include\emac.h</PATH>
      </HDR1EMAC>
      <HDR2EMAC>
        <PATH>include\hw_emac.h</PATH>
      </HDR2EMAC>
      <HDR3EMAC>
        <PATH>include\hw_emac_ctrl.h</PATH>
      </HDR3EMAC>
      <HDR4EMAC>
        <PATH>include\hw_mdio.h</PATH>
      </HDR4EMAC>
      <HDR5EMAC>
        <PATH>include\hw_reg_access.h</PATH>
      </HDR5EMAC>
      <HDR6EMAC>
        <PATH>include\mdio.h</PATH>
      </HDR6EMAC>
      <HDRPHY>
        <PATH>include\phy_dp83640.h</PATH>
      </HDRPHY>
      <SRC1EMAC>
        <PATH/>
      </SRC1EMAC>
      <SRC2EMAC>
        <PATH/>
      </SRC2EMAC>
      <SRCPHY>
        <PATH/>
      </SRCPHY>
    </FILENAMES>
  </EMAC>
  <DCC>
    <FILENAMES>
      <HDRDCC_R>
        <PATH>include\reg_dcc.h</PATH>
      </HDRDCC_R>
      <HDRDCC>
        <PATH>include\dcc.h</PATH>
      </HDRDCC>
      <SRCDCC>
        <PATH/>
      </SRCDCC>
    </FILENAMES>
  </DCC>
  <RTP>
    <FILENAMES>
      <HDRRTP_R>
        <PATH>include\reg_rtp.h</PATH>
      </HDRRTP_R>
      <HDRRTP>
        <PATH>include\rtp.h</PATH>
      </HDRRTP>
      <SRCRTP>
        <PATH/>
      </SRCRTP>
    </FILENAMES>
  </RTP>
  <DMM>
    <FILENAMES>
      <HDRDMM_R>
        <PATH>include\reg_dmm.h</PATH>
      </HDRDMM_R>
      <HDRDMM>
        <PATH>include\dmm.h</PATH>
      </HDRDMM>
      <SRCDMM>
        <PATH/>
      </SRCDMM>
    </FILENAMES>
  </DMM>
  <EMIF>
    <FILENAMES>
      <HDREMIF_R>
        <PATH>include\reg_emif.h</PATH>
      </HDREMIF_R>
      <HDREMIF>
        <PATH>include\emif.h</PATH>
      </HDREMIF>
      <SRCEMIF>
        <PATH/>
      </SRCEMIF>
    </FILENAMES>
  </EMIF>
  <POM>
    <FILENAMES>
      <HDRPOM_R>
        <PATH>include\reg_pom.h</PATH>
      </HDRPOM_R>
      <HDRPOM>
        <PATH>include\pom.h</PATH>
      </HDRPOM>
      <SRCPOM>
        <PATH/>
      </SRCPOM>
    </FILENAMES>
  </POM>
  <CRC>
    <FILENAMES>
      <HDRCRC_R>
        <PATH>include\reg_crc.h</PATH>
      </HDRCRC_R>
      <HDRCRC>
        <PATH>include\crc.h</PATH>
      </HDRCRC>
      <SRCCRC>
        <PATH/>
      </SRCCRC>
    </FILENAMES>
  </CRC>
  <FEE>
    <FILENAMES>
      <HDR1FEE>
        <PATH>include\Device_TMS570LS31.h</PATH>
      </HDR1FEE>
      <HDR2FEE>
        <PATH>include\Device_header.h</PATH>
      </HDR2FEE>
      <HDR3FEE>
        <PATH>include\Device_types.h</PATH>
      </HDR3FEE>
      <HDR4FEE>
        <PATH>include\ti_fee_Cfg.h</PATH>
      </HDR4FEE>
      <HDR5FEE>
        <PATH>include\MemMap.h</PATH>
      </HDR5FEE>
      <HDR6FEE>
        <PATH>include\ti_fee_types.h</PATH>
      </HDR6FEE>
      <HDR7FEE>
        <PATH>include\ti_fee.h</PATH>
      </HDR7FEE>
      <HDR8FEE>
        <PATH>include\fee_interface.h</PATH>
      </HDR8FEE>
      <SRC1FEE>
        <PATH/>
      </SRC1FEE>
      <SRC2FEE>
        <PATH/>
      </SRC2FEE>
      <SRC3FEE>
        <PATH/>
      </SRC3FEE>
      <SRC4FEE>
        <PATH/>
      </SRC4FEE>
      <SRC5FEE>
        <PATH/>
      </SRC5FEE>
      <SRC6FEE>
        <PATH/>
      </SRC6FEE>
      <SRC7FEE>
        <PATH/>
      </SRC7FEE>
      <SRC8FEE>
        <PATH/>
      </SRC8FEE>
      <SRC9FEE>
        <PATH/>
      </SRC9FEE>
      <SRC10FEE>
        <PATH/>
      </SRC10FEE>
      <SRC11FEE>
        <PATH/>
      </SRC11FEE>
      <SRC12FEE>
        <PATH/>
      </SRC12FEE>
      <SRC13FEE>
        <PATH/>
      </SRC13FEE>
      <SRC14FEE>
        <PATH/>
      </SRC14FEE>
      <SRC15FEE>
        <PATH/>
      </SRC15FEE>
      <SRC16FEE>
        <PATH/>
      </SRC16FEE>
    </FILENAMES>
  </FEE>
</SETTINGS>
