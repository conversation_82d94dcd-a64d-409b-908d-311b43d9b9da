# FIXED

ftu.obj: ../ftu.c
ftu.obj: ../ftu.h
ftu.obj: ../fray.h
ftu.obj: ../fray_node_config.h
ftu.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h
ftu.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h
ftu.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h
ftu.obj: C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h
ftu.obj: C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h

../ftu.c: 
../ftu.h: 
../fray.h: 
../fray_node_config.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_vim.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/reg_vim.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/sys_common.h: 
C:/Users/<USER>/workspace_v6_1/Flexray_Demo/include/hal_stdtypes.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdint.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdbool.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/yvals.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/stdarg.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/linkage.h: 
C:/ti/ccsv6/tools/compiler/ti-cgt-arm_5.2.2/include/_lock.h: 
