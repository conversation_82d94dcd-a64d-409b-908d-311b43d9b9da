******************************************************************************
                  TI ARM Linker PC v5.2.2                      
******************************************************************************
>> Linked Tue May 05 23:15:19 2015

OUTPUT FILE NAME:   <CSR_Flexray_Demo.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 00006470


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  VECTORS               00000000   00000020  00000020  00000000     X
  FLASH0                00000020   0017ffe0  0000776c  00178874  R  X
  FLASH1                00180000   00180000  00000000  00180000  R  X
  STACKS                08000000   00001500  00000000  00001500  RW  
  RAM                   08001500   0003eb00  000001a0  0003e960  RW  


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007790   00007790    r-x
  00000000    00000000    00000020   00000020    r-x .intvecs
  00000020    00000020    00007148   00007148    r-x .text
  00007168    00007168    000005e4   000005e4    r-- .const
  00007750    00007750    00000040   00000040    r-- .cinit
08001500    08001500    000001a0   00000000    rw-
  08001500    08001500    00000170   00000000    rw- .bss
  08001670    08001670    00000030   00000000    rw- .data


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    00000020     
                  00000000    00000020     sys_intvecs.obj (.intvecs)

.text      0    00000020    00007148     
                  00000020    00002db4     sys_selftest.obj (.text)
                  00002dd4    00000b20     system.obj (.text)
                  000038f4    000009f8     fray_test.obj (.text)
                  000042ec    00000890     sys_vim.obj (.text)
                  00004b7c    00000638     fray.obj (.text)
                  000051b4    00000634     esm.obj (.text)
                  000057e8    00000630     pinmux.obj (.text)
                  00005e18    000003c4     sys_core.obj (.text)
                  000061dc    00000294     sys_vim.obj (.text:retain)
                  00006470    00000264     sys_startup.obj (.text:retain)
                  000066d4    0000023c     LED_Show.obj (.text)
                  00006910    00000158     esm.obj (.text:retain)
                  00006a68    00000114     sys_pmu.obj (.text)
                  00006b7c    000000e8     dabort.obj (.text)
                  00006c64    000000e0     fray_test.obj (.text:retain)
                  00006d44    000000ac     notification.obj (.text)
                  00006df0    0000009c     rtsv7R4_T_be_v3D16_eabi.lib : memcpy_t2.obj (.text)
                  00006e8c    0000007a                                 : memset_t2.obj (.text)
                  00006f06    00000002     --HOLE-- [fill = 0]
                  00006f08    00000074     ftu_test.obj (.text:retain)
                  00006f7c    0000006c     rtsv7R4_T_be_v3D16_eabi.lib : autoinit.obj (.text)
                  00006fe8    0000006a                                 : copy_decompress_rle.obj (.text)
                  00007052    00000002     --HOLE-- [fill = 0]
                  00007054    00000050     sys_main.obj (.text)
                  000070a4    0000004c     rtsv7R4_T_be_v3D16_eabi.lib : cpy_tbl.obj (.text)
                  000070f0    00000040                                 : exit.obj (.text)
                  00007130    00000014                                 : _lock.obj (.text)
                  00007144    0000000e                                 : copy_decompress_none.obj (.text:decompress:none)
                  00007152    0000000c                                 : copy_zero_init.obj (.text:decompress:ZI)
                  0000715e    00000006                                 : copy_decompress_rle.obj (.text:decompress:rle24)
                  00007164    00000004     sys_phantom.obj (.text:retain)

.const     0    00007168    000005e4     
                  00007168    000002e8     fray_test.obj (.const:trans_data)
                  00007450    00000204     sys_vim.obj (.const:s_vim_init)
                  00007654    000000f8     fray_test.obj (.const:sync_data)

.cinit     0    00007750    00000040     
                  00007750    00000016     (.cinit..data.load) [load image, compression = rle]
                  00007766    00000002     --HOLE-- [fill = 0]
                  00007768    0000000c     (__TI_handler_table)
                  00007774    00000004     --HOLE-- [fill = 0]
                  00007778    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00007780    00000010     (__TI_cinit_table)

.bss       0    08001500    00000170     UNINITIALIZED
                  08001500    00000080     sys_main.obj (.bss:active_node)
                  08001580    00000044     fray_test.obj (.bss:Fr_Config)
                  080015c4    00000034     fray_test.obj (.bss:Fr_LPdu)
                  080015f8    00000024     fray_test.obj (.bss:Fr_LSdu1)
                  0800161c    00000024     fray_test.obj (.bss:Fr_LSdu2)
                  08001640    00000020     (.common:FTU_RxBuffer)
                  08001660    00000004     (.common:ERAY_TIBC_Int0)
                  08001664    00000004     (.common:ERAY_TOBC_Int0)
                  08001668    00000004     (.common:ERAY_TXI_Int0)
                  0800166c    00000004     (.common:FRAY_TestFlag)

.TI.noinit 
*          0    08001500    00000000     UNINITIALIZED

.data      0    08001670    00000030     UNINITIALIZED
                  08001670    0000000c     ftu_test.obj (.data)
                  0800167c    0000000c     sys_main.obj (.data)
                  08001688    00000008     LED_Show.obj (.data)
                  08001690    00000008     rtsv7R4_T_be_v3D16_eabi.lib : _lock.obj (.data)
                  08001698    00000008                                 : exit.obj (.data)

.TI.persistent 
*          0    08001670    00000000     UNINITIALIZED


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00007780 records: 2, size/record: 8, table size: 16
	.data: load addr=00007750, load size=00000016 bytes, run addr=08001670, run size=00000030 bytes, compression=rle
	.bss: load addr=00007778, load size=00000008 bytes, run addr=08001500, run size=00000170 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00007768 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_rle24
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                
-------   ----                                
000070f1  C$$EXIT                             
08001660  ERAY_TIBC_Int0                      
08001664  ERAY_TOBC_Int0                      
08001668  ERAY_TXI_Int0                       
00006c64  FRAY_ISR                            
0800166c  FRAY_TestFlag                       
000038f4  FRAY_TestInit                       
00006f08  FTU_ISR                             
08001678  FTU_Index                           
08001640  FTU_RxBuffer                        
08001674  FTU_Started                         
08001670  FTU_TestFlag                        
00003d78  FlexRay_Config                      
08001580  Fr_Config                           
080015c4  Fr_LPdu                             
080015f8  Fr_LSdu1                            
0800161c  Fr_LSdu2                            
00004d2c  Fray_AllowColdStart                 
0000392c  Fray_Init                           
00004dbc  Fray_Prepare_WRHS                   
00004fa0  Fray_RX_OutputBuffer                
00004b7c  Fray_RegSetting                     
00004d7c  Fray_StartCommunication             
00004ed8  Fray_TX_InputBuffer                 
00004ca0  Fray_toReadyState                   
000066d4  LED_Show                            
00003e54  Message_Buff_Config                 
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              
00007780  __TI_CINIT_Base                     
00007790  __TI_CINIT_Limit                    
00007768  __TI_Handler_Table_Base             
00007774  __TI_Handler_Table_Limit            
000061d4  __TI_PINIT_Base                     
000061d8  __TI_PINIT_Limit                    
00006f7d  __TI_auto_init                      
08001698  __TI_cleanup_ptr                    
00007145  __TI_decompress_none                
0000715f  __TI_decompress_rle24               
0800169c  __TI_dtors_ptr                      
00000000  __TI_static_base__                  
00007153  __TI_zero_init                      
00006e8d  __aeabi_memclr                      
00006e8d  __aeabi_memclr4                     
00006e8d  __aeabi_memclr8                     
00006df1  __aeabi_memcpy                      
00006df1  __aeabi_memcpy4                     
00006df1  __aeabi_memcpy8                     
00006e8f  __aeabi_memset                      
00006e8f  __aeabi_memset4                     
00006e8f  __aeabi_memset8                     
ffffffff  __binit__                           
ffffffff  __c_args__                          
00006470  _c_int00                            
000060cc  _coreClearAuxiliaryDataFault_       
000060e8  _coreClearAuxiliaryInstructionFault_
00006094  _coreClearDataFaultAddress_         
0000605c  _coreClearDataFault_                
000060b0  _coreClearInstructionFaultAddress_  
00006078  _coreClearInstructionFault_         
00005fc0  _coreDisableEventBusExport_         
00006024  _coreDisableFlashEcc_               
00005ff0  _coreDisableRamEcc_                 
00005fa8  _coreEnableEventBusExport_          
00006008  _coreEnableFlashEcc_                
0000603c  _coreEnableIrqVicOffset_            
00005fd8  _coreEnableRamEcc_                  
00005f90  _coreEnableVfp_                     
000060c4  _coreGetAuxiliaryDataFault_         
000060e0  _coreGetAuxiliaryInstructionFault_  
0000608c  _coreGetDataFaultAddress_           
00006054  _coreGetDataFault_                  
000060a8  _coreGetInstructionFaultAddress_    
00006070  _coreGetInstructionFault_           
00005e18  _coreInitRegisters_                 
00005f24  _coreInitStackPointer_              
00006b7c  _dabort                             
00006104  _disable_FIQ_interrupt_             
0000610c  _disable_IRQ_interrupt_             
000060fc  _disable_interrupt_                 
00006114  _enable_interrupt_                  
000061a4  _errata_CORTEXR4_57_                
000061bc  _errata_CORTEXR4_66_                
0000611c  _esmCcmErrorsClear_                 
00005f70  _getCPSRValue_                      
00005f78  _gotoCPUIdle_                       
08001690  _lock                               
0000713f  _nop                                
00006ad0  _pmuDisableCountersGlobal_          
00006ab8  _pmuEnableCountersGlobal_           
00006b50  _pmuGetCycleCount_                  
00006b58  _pmuGetEventCount_                  
00006b68  _pmuGetOverflow_                    
00006a68  _pmuInit_                           
00006b18  _pmuResetCounters_                  
00006ae8  _pmuResetCycleCounter_              
00006b00  _pmuResetEventCounters_             
00006b40  _pmuSetCountEvent_                  
00006b30  _pmuStartCounters_                  
00006b38  _pmuStopCounters_                   
00007137  _register_lock                      
00007131  _register_unlock                    
08001694  _unlock                             
000070f5  abort                               
08001500  active_node                         
000012e8  adc1ParityCheck                     
000013d8  adc2ParityCheck                     
ffffffff  binit                               
00001480  can1ParityCheck                     
00001558  can2ParityCheck                     
00001620  can3ParityCheck                     
00000030  ccmSelfCheck                        
000027d8  ccmr4GetConfigValue                 
000009a4  checkB0RAMECC                       
00000b94  checkB1RAMECC                       
00001c88  checkClockMonitor                   
00000d84  checkFlashECC                       
00001d64  checkFlashEEPROMECC                 
00001efc  checkPLL1Slip                       
00001ff8  checkPLL2Slip                       
000020c0  checkRAMAddrParity                  
00001a40  checkRAMECC                         
000021e0  checkRAMUERRTest                    
00000828  checkefcSelfTest                    
00004220  clear_WRDS                          
000070a5  copy_in                             
00000234  cpuSelfTest                         
00000e68  cpuSelfTestFail                     
00000e60  custom_dabort                       
00002c34  disableParity                       
00006ddc  dmaGroupANotification               
00000f18  dmaParityCheck                      
00006db4  edgeNotification                    
000006cc  efcCheck                            
00002740  efcGetConfigValue                   
00000800  efcSelfTest                         
00000754  efcStuckZeroTest                    
00002ba8  enableParity                        
00002844  errata_PBIST_4                      
0800167c  error                               
00005364  esmActivateNormalOperation          
00005450  esmClearStatus                      
00005490  esmClearStatusBuffer                
00005328  esmDisableError                     
000053a0  esmDisableInterrupt                 
000052fc  esmEnableError                      
00005374  esmEnableInterrupt                  
000055b0  esmEnterSelfTest                    
000052e0  esmError                            
00005688  esmGetConfigValue                   
000054dc  esmGetStatus                        
00005550  esmGetStatusBuffer                  
00006d44  esmGroup1Notification               
00006d54  esmGroup2Notification               
00006910  esmHighInterrupt                    
000051b4  esmInit                             
00005638  esmSelfTestStatus                   
000054bc  esmSetCounterPreloadValue           
000053cc  esmSetInterruptLevel                
00005354  esmTriggerErrorPinReset             
000070fd  exit                                
00002344  fmcBus1ParityCheck                  
000008c8  fmcBus2Check                        
00000904  fmcECCcheck                         
00005064  header_crc_calc                     
00000fd0  het1ParityCheck                     
00001144  het2ParityCheck                     
00006dc8  hetNotification                     
00001080  htu1ParityCheck                     
00001228  htu2ParityCheck                     
08001680  led_show                            
00007054  main                                
00002f10  mapClocks                           
00006df1  memcpy                              
00000174  memoryInit                          
00006d64  memoryPort0TestFailNotification     
00006d80  memoryPort1TestFailNotification     
00006e95  memset                              
000016f4  mibspi1ParityCheck                  
000017fc  mibspi3ParityCheck                  
00001920  mibspi5ParityCheck                  
000057e8  muxInit                             
08001684  node_num                            
00002470  pbistFail                           
00002558  pbistGetConfigValue                 
000005d0  pbistIsTestCompleted                
000005fc  pbistIsTestPassed                   
00000650  pbistPortTestStatus                 
000004d4  pbistRun                            
000002cc  pbistSelfCheck                      
000005a0  pbistStop                           
00002ebc  periphInit                          
00007164  phantomInterrupt                    
00005b6c  pinmuxGetConfigValue                
00006d9c  pwmNotification                     
00000000  resetEntry                          
00000020  selftestFailNotification            
00002e7c  setupFlash                          
00002dd4  setupPLL                            
000036d8  sramGetConfigValue                  
000026a0  stcGetConfigValue                   
000001b8  stcSelfCheck                        
00000e64  stcSelfCheckFail                    
00007654  sync_data                           
00003118  systemGetConfigValue                
0000301c  systemInit                          
000030e8  systemPowerDown                     
0000352c  tcmflashGetConfigValue              
00007168  trans_data                          
00003a24  transmit_check_node                 
00002e34  trimLPO                             
000043c0  vimChannelMap                       
000045b4  vimDisableInterrupt                 
0000445c  vimEnableInterrupt                  
00004638  vimGetConfigValue                   
000042ec  vimInit                             
00000e6c  vimParityCheck                      
000061dc  vimParityErrorHandler               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                
-------   ----                                
00000000  __TI_static_base__                  
00000000  resetEntry                          
00000020  selftestFailNotification            
00000030  ccmSelfCheck                        
00000174  memoryInit                          
000001b8  stcSelfCheck                        
00000234  cpuSelfTest                         
000002cc  pbistSelfCheck                      
000004d4  pbistRun                            
000005a0  pbistStop                           
000005d0  pbistIsTestCompleted                
000005fc  pbistIsTestPassed                   
00000650  pbistPortTestStatus                 
000006cc  efcCheck                            
00000754  efcStuckZeroTest                    
00000800  efcSelfTest                         
00000828  checkefcSelfTest                    
000008c8  fmcBus2Check                        
00000904  fmcECCcheck                         
000009a4  checkB0RAMECC                       
00000b94  checkB1RAMECC                       
00000d84  checkFlashECC                       
00000e60  custom_dabort                       
00000e64  stcSelfCheckFail                    
00000e68  cpuSelfTestFail                     
00000e6c  vimParityCheck                      
00000f18  dmaParityCheck                      
00000fd0  het1ParityCheck                     
00001080  htu1ParityCheck                     
00001144  het2ParityCheck                     
00001228  htu2ParityCheck                     
000012e8  adc1ParityCheck                     
000013d8  adc2ParityCheck                     
00001480  can1ParityCheck                     
00001558  can2ParityCheck                     
00001620  can3ParityCheck                     
000016f4  mibspi1ParityCheck                  
000017fc  mibspi3ParityCheck                  
00001920  mibspi5ParityCheck                  
00001a40  checkRAMECC                         
00001c88  checkClockMonitor                   
00001d64  checkFlashEEPROMECC                 
00001efc  checkPLL1Slip                       
00001ff8  checkPLL2Slip                       
000020c0  checkRAMAddrParity                  
000021e0  checkRAMUERRTest                    
00002344  fmcBus1ParityCheck                  
00002470  pbistFail                           
00002558  pbistGetConfigValue                 
000026a0  stcGetConfigValue                   
00002740  efcGetConfigValue                   
000027d8  ccmr4GetConfigValue                 
00002844  errata_PBIST_4                      
00002ba8  enableParity                        
00002c34  disableParity                       
00002dd4  setupPLL                            
00002e34  trimLPO                             
00002e7c  setupFlash                          
00002ebc  periphInit                          
00002f10  mapClocks                           
0000301c  systemInit                          
000030e8  systemPowerDown                     
00003118  systemGetConfigValue                
0000352c  tcmflashGetConfigValue              
000036d8  sramGetConfigValue                  
000038f4  FRAY_TestInit                       
0000392c  Fray_Init                           
00003a24  transmit_check_node                 
00003d78  FlexRay_Config                      
00003e54  Message_Buff_Config                 
00004220  clear_WRDS                          
000042ec  vimInit                             
000043c0  vimChannelMap                       
0000445c  vimEnableInterrupt                  
000045b4  vimDisableInterrupt                 
00004638  vimGetConfigValue                   
00004b7c  Fray_RegSetting                     
00004ca0  Fray_toReadyState                   
00004d2c  Fray_AllowColdStart                 
00004d7c  Fray_StartCommunication             
00004dbc  Fray_Prepare_WRHS                   
00004ed8  Fray_TX_InputBuffer                 
00004fa0  Fray_RX_OutputBuffer                
00005064  header_crc_calc                     
000051b4  esmInit                             
000052e0  esmError                            
000052fc  esmEnableError                      
00005328  esmDisableError                     
00005354  esmTriggerErrorPinReset             
00005364  esmActivateNormalOperation          
00005374  esmEnableInterrupt                  
000053a0  esmDisableInterrupt                 
000053cc  esmSetInterruptLevel                
00005450  esmClearStatus                      
00005490  esmClearStatusBuffer                
000054bc  esmSetCounterPreloadValue           
000054dc  esmGetStatus                        
00005550  esmGetStatusBuffer                  
000055b0  esmEnterSelfTest                    
00005638  esmSelfTestStatus                   
00005688  esmGetConfigValue                   
000057e8  muxInit                             
00005b6c  pinmuxGetConfigValue                
00005e18  _coreInitRegisters_                 
00005f24  _coreInitStackPointer_              
00005f70  _getCPSRValue_                      
00005f78  _gotoCPUIdle_                       
00005f90  _coreEnableVfp_                     
00005fa8  _coreEnableEventBusExport_          
00005fc0  _coreDisableEventBusExport_         
00005fd8  _coreEnableRamEcc_                  
00005ff0  _coreDisableRamEcc_                 
00006008  _coreEnableFlashEcc_                
00006024  _coreDisableFlashEcc_               
0000603c  _coreEnableIrqVicOffset_            
00006054  _coreGetDataFault_                  
0000605c  _coreClearDataFault_                
00006070  _coreGetInstructionFault_           
00006078  _coreClearInstructionFault_         
0000608c  _coreGetDataFaultAddress_           
00006094  _coreClearDataFaultAddress_         
000060a8  _coreGetInstructionFaultAddress_    
000060b0  _coreClearInstructionFaultAddress_  
000060c4  _coreGetAuxiliaryDataFault_         
000060cc  _coreClearAuxiliaryDataFault_       
000060e0  _coreGetAuxiliaryInstructionFault_  
000060e8  _coreClearAuxiliaryInstructionFault_
000060fc  _disable_interrupt_                 
00006104  _disable_FIQ_interrupt_             
0000610c  _disable_IRQ_interrupt_             
00006114  _enable_interrupt_                  
0000611c  _esmCcmErrorsClear_                 
000061a4  _errata_CORTEXR4_57_                
000061bc  _errata_CORTEXR4_66_                
000061d4  __TI_PINIT_Base                     
000061d8  __TI_PINIT_Limit                    
000061dc  vimParityErrorHandler               
00006470  _c_int00                            
000066d4  LED_Show                            
00006910  esmHighInterrupt                    
00006a68  _pmuInit_                           
00006ab8  _pmuEnableCountersGlobal_           
00006ad0  _pmuDisableCountersGlobal_          
00006ae8  _pmuResetCycleCounter_              
00006b00  _pmuResetEventCounters_             
00006b18  _pmuResetCounters_                  
00006b30  _pmuStartCounters_                  
00006b38  _pmuStopCounters_                   
00006b40  _pmuSetCountEvent_                  
00006b50  _pmuGetCycleCount_                  
00006b58  _pmuGetEventCount_                  
00006b68  _pmuGetOverflow_                    
00006b7c  _dabort                             
00006c64  FRAY_ISR                            
00006d44  esmGroup1Notification               
00006d54  esmGroup2Notification               
00006d64  memoryPort0TestFailNotification     
00006d80  memoryPort1TestFailNotification     
00006d9c  pwmNotification                     
00006db4  edgeNotification                    
00006dc8  hetNotification                     
00006ddc  dmaGroupANotification               
00006df1  __aeabi_memcpy                      
00006df1  __aeabi_memcpy4                     
00006df1  __aeabi_memcpy8                     
00006df1  memcpy                              
00006e8d  __aeabi_memclr                      
00006e8d  __aeabi_memclr4                     
00006e8d  __aeabi_memclr8                     
00006e8f  __aeabi_memset                      
00006e8f  __aeabi_memset4                     
00006e8f  __aeabi_memset8                     
00006e95  memset                              
00006f08  FTU_ISR                             
00006f7d  __TI_auto_init                      
00007054  main                                
000070a5  copy_in                             
000070f1  C$$EXIT                             
000070f5  abort                               
000070fd  exit                                
00007131  _register_unlock                    
00007137  _register_lock                      
0000713f  _nop                                
00007145  __TI_decompress_none                
00007153  __TI_zero_init                      
0000715f  __TI_decompress_rle24               
00007164  phantomInterrupt                    
00007168  trans_data                          
00007654  sync_data                           
00007768  __TI_Handler_Table_Base             
00007774  __TI_Handler_Table_Limit            
00007780  __TI_CINIT_Base                     
00007790  __TI_CINIT_Limit                    
08001500  active_node                         
08001580  Fr_Config                           
080015c4  Fr_LPdu                             
080015f8  Fr_LSdu1                            
0800161c  Fr_LSdu2                            
08001640  FTU_RxBuffer                        
08001660  ERAY_TIBC_Int0                      
08001664  ERAY_TOBC_Int0                      
08001668  ERAY_TXI_Int0                       
0800166c  FRAY_TestFlag                       
08001670  FTU_TestFlag                        
08001674  FTU_Started                         
08001678  FTU_Index                           
0800167c  error                               
08001680  led_show                            
08001684  node_num                            
08001690  _lock                               
08001694  _unlock                             
08001698  __TI_cleanup_ptr                    
0800169c  __TI_dtors_ptr                      
ffffffff  __binit__                           
ffffffff  __c_args__                          
ffffffff  binit                               
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              

[218 symbols]
