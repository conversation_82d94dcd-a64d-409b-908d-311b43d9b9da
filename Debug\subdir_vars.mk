################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../LED_Show.c \
../file_io.c \
../fray.c \
../fray_node0.c \
../fray_node1.c \
../fray_node2.c \
../fray_node3.c \
../ftu.c \
../ftu_node0.c \
../ftu_node1.c \
../ftu_node2.c \
../sci_common.c \
../sys_main.c 

C_DEPS += \
./LED_Show.d \
./file_io.d \
./fray.d \
./fray_node0.d \
./fray_node1.d \
./fray_node2.d \
./fray_node3.d \
./ftu.d \
./ftu_node0.d \
./ftu_node1.d \
./ftu_node2.d \
./sci_common.d \
./sys_main.d 

OBJS += \
./LED_Show.obj \
./file_io.obj \
./fray.obj \
./fray_node0.obj \
./fray_node1.obj \
./fray_node2.obj \
./fray_node3.obj \
./ftu.obj \
./ftu_node0.obj \
./ftu_node1.obj \
./ftu_node2.obj \
./sci_common.obj \
./sys_main.obj 

OBJS__QUOTED += \
"LED_Show.obj" \
"file_io.obj" \
"fray.obj" \
"fray_node0.obj" \
"fray_node1.obj" \
"fray_node2.obj" \
"fray_node3.obj" \
"ftu.obj" \
"ftu_node0.obj" \
"ftu_node1.obj" \
"ftu_node2.obj" \
"sci_common.obj" \
"sys_main.obj" 

C_DEPS__QUOTED += \
"LED_Show.d" \
"file_io.d" \
"fray.d" \
"fray_node0.d" \
"fray_node1.d" \
"fray_node2.d" \
"fray_node3.d" \
"ftu.d" \
"ftu_node0.d" \
"ftu_node1.d" \
"ftu_node2.d" \
"sci_common.d" \
"sys_main.d" 

C_SRCS__QUOTED += \
"../LED_Show.c" \
"../file_io.c" \
"../fray.c" \
"../fray_node0.c" \
"../fray_node1.c" \
"../fray_node2.c" \
"../fray_node3.c" \
"../ftu.c" \
"../ftu_node0.c" \
"../ftu_node1.c" \
"../ftu_node2.c" \
"../sci_common.c" \
"../sys_main.c" 


