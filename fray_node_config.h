/*
 * fray_node_config.h
 *
 *  Created on: Oct 5, 2015
 *      Author: a0324020
 */

#ifndef FRAY_NODE_CONFIG_H_
#define FRAY_NODE_CONFIG_H_

/* number of Node: 0, 1,2,3,...*/
#define FrayNodeNumber  0

// save the value of several status registers and error registers to a file
// CCSV, CCEV, SCV, TXRQ1, TXRQ2, TXRQ3, TXRQ4, EIR, SIR, etc
//#define EXPORT_FRAY_STATUS

// The test mode features are intended for hardware testing or for FlexRay bus analyzer tools. They are not
// intended to be used in FlexRay applications.
// To return from test mode operation to regular FlexRay operation we strongly recommend to
// apply a hardware reset (Power on Reset or nReset) to reset all FlexRay internal state
// machines to their initial state.
#define FRAY_TEST_MODE     0

//*****************************************************************************
// Using FTU to transfer data between system memory and Fray MSG RAM
// 1 or 0
//*****************************************************************************
#define FRAY_ENABLE_FTU    1

#if (FRAY_ENABLE_FTU == 1)
#define TransferOnEventToSRAM       1
#endif

// Measurement Method used
//#define PMU_CYCLE   //used in fray_node2.c


#endif /* FRAY_NODE_CONFIG_H_ */
