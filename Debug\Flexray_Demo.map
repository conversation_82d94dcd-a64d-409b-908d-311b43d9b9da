******************************************************************************
                  TI ARM Linker PC v5.2.2                      
******************************************************************************
>> Linked Fri Nov 13 15:23:53 2015

OUTPUT FILE NAME:   <Flexray_Demo.out>
ENTRY POINT SYMBOL: "_c_int00"  address: 00007640


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  VECTORS               00000000   00000020  00000020  00000000     X
  FLASH0                00000020   0017ffe0  000087e4  001777fc  R  X
  FLASH1                00180000   00180000  00000000  00180000  R  X
  STACKS                08000000   00001500  00000000  00001500  RW  
  RAM                   08001500   0003eb00  00000ad0  0003e030  RW  


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008808   00008808    r-x
  00000000    00000000    00000020   00000020    r-x .intvecs
  00000020    00000020    000085c4   000085c4    r-x .text
  000085e4    000085e4    00000180   00000180    r-- .const
  00008768    00008768    000000a0   000000a0    r-- .cinit
08001500    08001500    00000ad0   00000000    rw-
  08001500    08001500    00000800   00000000    rw- .sysmem
  08001d00    08001d00    00000230   00000000    rw- .bss
  08001f30    08001f30    000000a0   00000000    rw- .data


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    00000020     
                  00000000    00000020     sys_intvecs.obj (.intvecs)

.text      0    00000020    000085c4     
                  00000020    00002cdc     sys_selftest.obj (.text)
                  00002cfc    00000b34     system.obj (.text)
                  00003830    00000930     fray.obj (.text)
                  00004160    00000930     ftu_node0.obj (.text)
                  00004a90    00000898     sys_vim.obj (.text)
                  00005328    00000750     sci.obj (.text)
                  00005a78    00000630     pinmux.obj (.text)
                  000060a8    0000062c     esm.obj (.text)
                  000066d4    00000484     gio.obj (.text)
                  00006b58    000003e8     ftu.obj (.text)
                  00006f40    000003c4     sys_core.obj (.text)
                  00007304    0000033c     sci_common.obj (.text)
                  00007640    00000298     sys_startup.obj (.text:retain)
                  000078d8    00000294     sys_vim.obj (.text:retain)
                  00007b6c    00000158     esm.obj (.text:retain)
                  00007cc4    00000148     fray.obj (.text:retain)
                  00007e0c    00000114     sys_pmu.obj (.text)
                  00007f20    000000e8     dabort.obj (.text)
                  00008008    000000d4     notification.obj (.text)
                  000080dc    000000bc     ftu.obj (.text:retain)
                  00008198    0000009c     rtsv7R4_T_be_v3D16_eabi.lib : memcpy_t2.obj (.text)
                  00008234    0000007a                                 : memset_t2.obj (.text)
                  000082ae    00000002     --HOLE-- [fill = 0]
                  000082b0    00000070     LED_Show.obj (.text)
                  00008320    0000006c     rtsv7R4_T_be_v3D16_eabi.lib : autoinit.obj (.text)
                  0000838c    0000006a                                 : copy_decompress_rle.obj (.text)
                  000083f6    00000002     --HOLE-- [fill = 0]
                  000083f8    00000058                                 : i_div32.obj (.text)
                  00008450    00000054                                 : u_div32.obj (.text)
                  000084a4    00000054     sys_main.obj (.text)
                  000084f8    0000004c     rtsv7R4_T_be_v3D16_eabi.lib : cpy_tbl.obj (.text)
                  00008544    00000040                                 : exit.obj (.text)
                  00008584    00000016                                 : strlen.obj (.text)
                  0000859a    00000002     --HOLE-- [fill = 0]
                  0000859c    00000014     sys_phantom.obj (.text:retain)
                  000085b0    00000014     rtsv7R4_T_be_v3D16_eabi.lib : _lock.obj (.text)
                  000085c4    0000000e                                 : copy_decompress_none.obj (.text:decompress:none)
                  000085d2    0000000c                                 : copy_zero_init.obj (.text:decompress:ZI)
                  000085de    00000006                                 : copy_decompress_rle.obj (.text:decompress:rle24)

.const     0    000085e4    00000180     
                  000085e4    00000180     sys_vim.obj (.const:s_vim_init)

.cinit     0    00008768    000000a0     
                  00008768    00000077     (.cinit..data.load) [load image, compression = rle]
                  000087df    00000001     --HOLE-- [fill = 0]
                  000087e0    0000000c     (__TI_handler_table)
                  000087ec    00000004     --HOLE-- [fill = 0]
                  000087f0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000087f8    00000010     (__TI_cinit_table)

.sysmem    0    08001500    00000800     UNINITIALIZED
                  08001500    00000008     rtsv7R4_T_be_v3D16_eabi.lib : memory.obj (.sysmem)
                  08001508    000007f8     --HOLE--

.bss       0    08001d00    00000230     UNINITIALIZED
                  08001d00    00000200     ftu_node0.obj (.bss:FRAY_SysMemory)
                  08001f00    00000028     sci.obj (.bss:g_sciTransfer_t)
                  08001f28    00000004     (.common:g_ftuDataRCVED)
                  08001f2c    00000004     (.common:g_ftuErrorFlag)

.TI.noinit 
*          0    08001d00    00000000     UNINITIALIZED

.data      0    08001f30    000000a0     UNINITIALIZED
                  08001f30    00000090     ftu_node0.obj (.data:FRAY_TX_Payload)
                  08001fc0    00000008     rtsv7R4_T_be_v3D16_eabi.lib : _lock.obj (.data)
                  08001fc8    00000008                                 : exit.obj (.data)

.TI.persistent 
*          0    08001f30    00000000     UNINITIALIZED


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000087f8 records: 2, size/record: 8, table size: 16
	.data: load addr=00008768, load size=00000077 bytes, run addr=08001f30, run size=000000a0 bytes, compression=rle
	.bss: load addr=000087f0, load size=00000008 bytes, run addr=08001d00, run size=00000230 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000087e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_zero_init
	index: 1, handler: __TI_decompress_rle24
	index: 2, handler: __TI_decompress_none


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                
-------   ----                                
00008545  C$$EXIT                             
08001d00  FRAY_SysMemory                      
08001f30  FRAY_TX_Payload                     
00006cd0  FTU_Disable                         
00006ec0  FTU_ETESM_Clear                     
00006cb0  FTU_Enable                          
00006ea0  FTU_Error_INT_Enable                
00006e30  FTU_Event_ETESMS                    
00006b58  FTU_Init                            
00006c24  FTU_TCR_Init                        
00006d40  FTU_TOCC_IntEnable                  
00006de0  FTU_TOCC_Trigger                    
00006cf0  FTU_TOSM_IntEnable                  
00006d90  FTU_TOSM_Trigger                    
00006ee8  FTU_TTCC_Clear                      
00006f10  FTU_TTSM_Clear                      
00004160  FTU_Test                            
00006e80  FTU_Transfer_INT_Enable             
00003d94  Fray_AllowColdStart                 
00003b2c  Fray_CC_CONFIG                      
00003b78  Fray_CC_READY                       
00003ac0  Fray_INT_Disable                    
00003b0c  Fray_INT_Enable                     
0000388c  Fray_Init                           
00004074  Fray_NormalMode                     
000039a4  Fray_PrepareHeader                  
00003d0c  Fray_Receive                        
00003de4  Fray_StartCommunication             
00003f94  Fray_TestMode                       
00003c5c  Fray_Transmit                       
00003830  Fray_testInit                       
00007614  GetChar                             
00007344  Int2Str                             
000075ec  PrintChar                           
00007484  PrintInteger                        
00007438  PrintString                         
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              
00000800  __SYSMEM_SIZE                       
000087f8  __TI_CINIT_Base                     
00008808  __TI_CINIT_Limit                    
000087e0  __TI_Handler_Table_Base             
000087ec  __TI_Handler_Table_Limit            
000072fc  __TI_PINIT_Base                     
00007300  __TI_PINIT_Limit                    
00008321  __TI_auto_init                      
08001fc8  __TI_cleanup_ptr                    
000085c5  __TI_decompress_none                
000085df  __TI_decompress_rle24               
08001fcc  __TI_dtors_ptr                      
00000000  __TI_static_base__                  
000085d3  __TI_zero_init                      
000083f8  __aeabi_idiv                        
000083f8  __aeabi_idivmod                     
00008235  __aeabi_memclr                      
00008235  __aeabi_memclr4                     
00008235  __aeabi_memclr8                     
00008199  __aeabi_memcpy                      
00008199  __aeabi_memcpy4                     
00008199  __aeabi_memcpy8                     
00008237  __aeabi_memset                      
00008237  __aeabi_memset4                     
00008237  __aeabi_memset8                     
00008450  __aeabi_uidiv                       
00008450  __aeabi_uidivmod                    
ffffffff  __binit__                           
ffffffff  __c_args__                          
00007640  _c_int00                            
000071f4  _coreClearAuxiliaryDataFault_       
00007210  _coreClearAuxiliaryInstructionFault_
000071bc  _coreClearDataFaultAddress_         
00007184  _coreClearDataFault_                
000071d8  _coreClearInstructionFaultAddress_  
000071a0  _coreClearInstructionFault_         
000070e8  _coreDisableEventBusExport_         
0000714c  _coreDisableFlashEcc_               
00007118  _coreDisableRamEcc_                 
000070d0  _coreEnableEventBusExport_          
00007130  _coreEnableFlashEcc_                
00007164  _coreEnableIrqVicOffset_            
00007100  _coreEnableRamEcc_                  
000070b8  _coreEnableVfp_                     
000071ec  _coreGetAuxiliaryDataFault_         
00007208  _coreGetAuxiliaryInstructionFault_  
000071b4  _coreGetDataFaultAddress_           
0000717c  _coreGetDataFault_                  
000071d0  _coreGetInstructionFaultAddress_    
00007198  _coreGetInstructionFault_           
00006f40  _coreInitRegisters_                 
0000704c  _coreInitStackPointer_              
00007f20  _dabort                             
0000722c  _disable_FIQ_interrupt_             
00007234  _disable_IRQ_interrupt_             
00007224  _disable_interrupt_                 
0000723c  _enable_interrupt_                  
000072cc  _errata_CORTEXR4_57_                
000072e4  _errata_CORTEXR4_66_                
00007244  _esmCcmErrorsClear_                 
00007098  _getCPSRValue_                      
000070a0  _gotoCPUIdle_                       
08001fc0  _lock                               
000085bf  _nop                                
00007e78  _pmuDisableCountersGlobal_          
00007e60  _pmuEnableCountersGlobal_           
00007ef4  _pmuGetCycleCount_                  
00007efc  _pmuGetEventCount_                  
00007f0c  _pmuGetOverflow_                    
00007e0c  _pmuInit_                           
00007ec0  _pmuResetCounters_                  
00007e90  _pmuResetCycleCounter_              
00007ea8  _pmuResetEventCounters_             
00007ee8  _pmuSetCountEvent_                  
00007ed8  _pmuStartCounters_                  
00007ee0  _pmuStopCounters_                   
000085b7  _register_lock                      
000085b1  _register_unlock                    
08001500  _sys_memory                         
08001fc4  _unlock                             
00008549  abort                               
0000126c  adc1ParityCheck                     
00001358  adc2ParityCheck                     
00007304  append                              
ffffffff  binit                               
00001410  can1ParityCheck                     
000014e8  can2ParityCheck                     
000015b0  can3ParityCheck                     
00000030  ccmSelfCheck                        
000026fc  ccmr4GetConfigValue                 
00000938  checkB0RAMECC                       
00000b28  checkB1RAMECC                       
00001c18  checkClockMonitor                   
00000d18  checkFlashECC                       
00001cf4  checkFlashEEPROMECC                 
00001e8c  checkPLL1Slip                       
00001f8c  checkPLL2Slip                       
00002050  checkRAMAddrParity                  
000019d0  checkRAMECC                         
0000215c  checkRAMUERRTest                    
000007bc  checkefcSelfTest                    
000084f9  copy_in                             
00000234  cpuSelfTest                         
00000dfc  cpuSelfTestFail                     
00000df4  custom_dabort                       
00002b58  disableParity                       
000080c8  dmaGroupANotification               
00000eac  dmaParityCheck                      
000080a0  edgeNotification                    
00000660  efcCheck                            
00002660  efcGetConfigValue                   
00000794  efcSelfTest                         
000006e8  efcStuckZeroTest                    
00002ad0  enableParity                        
0000276c  errata_PBIST_4                      
00006258  esmActivateNormalOperation          
0000633c  esmClearStatus                      
0000637c  esmClearStatusBuffer                
0000621c  esmDisableError                     
00006294  esmDisableInterrupt                 
000061f0  esmEnableError                      
00006268  esmEnableInterrupt                  
0000649c  esmEnterSelfTest                    
000061d4  esmError                            
00006574  esmGetConfigValue                   
000063c8  esmGetStatus                        
0000643c  esmGetStatusBuffer                  
00008008  esmGroup1Notification               
00008018  esmGroup2Notification               
00007b6c  esmHighInterrupt                    
000060a8  esmInit                             
00006524  esmSelfTestStatus                   
000063a8  esmSetCounterPreloadValue           
000062c0  esmSetInterruptLevel                
00006248  esmTriggerErrorPinReset             
00008551  exit                                
000022c4  fmcBus1ParityCheck                  
0000085c  fmcBus2Check                        
00000898  fmcECCcheck                         
000049ac  frayErrorNotification               
00007cc4  frayHighLevelInterrupt              
00007d6c  frayLowLevelInterrupt               
000049c4  frayStatusChangeNotification        
00008148  ftuErrorInterrupt                   
000048a0  ftuErrorNotification                
00004960  ftuStatusChangeNotification         
000080dc  ftuTransferInterrupt                
08001f28  g_ftuDataRCVED                      
08001f2c  g_ftuErrorFlag                      
00006934  gioDisableNotification              
000068cc  gioEnableNotification               
00006834  gioGetBit                           
0000699c  gioGetConfigValue                   
0000685c  gioGetPort                          
000066d4  gioInit                             
00008060  gioNotification                     
000067c4  gioSetBit                           
000067a4  gioSetDirection                     
00006814  gioSetPort                          
00006874  gioToggleBit                        
000082b0  gio_LED_Blinky                      
00003e24  header_crc_calc                     
00000f58  het1ParityCheck                     
000010c0  het2ParityCheck                     
000080b4  hetNotification                     
00001004  htu1ParityCheck                     
000011bc  htu2ParityCheck                     
000084a4  main                                
00002e38  mapClocks                           
00008199  memcpy                              
00000174  memoryInit                          
00008028  memoryPort0TestFailNotification     
00008044  memoryPort1TestFailNotification     
0000823d  memset                              
00001684  mibspi1ParityCheck                  
0000178c  mibspi3ParityCheck                  
000018b0  mibspi5ParityCheck                  
00005a78  muxInit                             
000023ec  pbistFail                           
00002470  pbistGetConfigValue                 
000005a8  pbistIsTestCompleted                
000005d4  pbistIsTestPassed                   
00000608  pbistPortTestStatus                 
000004ac  pbistRun                            
000002cc  pbistSelfCheck                      
00000578  pbistStop                           
00002de4  periphInit                          
0000859c  phantomInterrupt                    
00005dfc  pinmuxGetConfigValue                
00008088  pwmNotification                     
00000000  resetEntry                          
000057d8  sciDisableLoopback                  
00005864  sciDisableNotification              
000057a4  sciEnableLoopback                   
000057fc  sciEnableNotification               
000058cc  sciGetConfigValue                   
00005328  sciInit                             
00005654  sciIsIdleDetected                   
00005638  sciIsRxReady                        
000054b8  sciIsTxReady                        
00008074  sciNotification                     
000056cc  sciReceive                          
000056a0  sciReceiveByte                      
00005670  sciRxError                          
00005504  sciSend                             
000054d4  sciSendByte                         
00005434  sciSetBaudrate                      
00005414  sciSetFunctional                    
00000020  selftestFailNotification            
00002da4  setupFlash                          
00002cfc  setupPLL                            
0000361c  sramGetConfigValue                  
000025bc  stcGetConfigValue                   
000001b8  stcSelfCheck                        
00000df8  stcSelfCheckFail                    
00008585  strlen                              
0000305c  systemGetConfigValue                
00002f60  systemInit                          
0000302c  systemPowerDown                     
00003470  tcmflashGetConfigValue              
00002d5c  trimLPO                             
00004b64  vimChannelMap                       
00004d58  vimDisableInterrupt                 
00004c00  vimEnableInterrupt                  
00004ddc  vimGetConfigValue                   
00004a90  vimInit                             
00000e00  vimParityCheck                      
000078d8  vimParityErrorHandler               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                
-------   ----                                
00000000  __TI_static_base__                  
00000000  resetEntry                          
00000020  selftestFailNotification            
00000030  ccmSelfCheck                        
00000174  memoryInit                          
000001b8  stcSelfCheck                        
00000234  cpuSelfTest                         
000002cc  pbistSelfCheck                      
000004ac  pbistRun                            
00000578  pbistStop                           
000005a8  pbistIsTestCompleted                
000005d4  pbistIsTestPassed                   
00000608  pbistPortTestStatus                 
00000660  efcCheck                            
000006e8  efcStuckZeroTest                    
00000794  efcSelfTest                         
000007bc  checkefcSelfTest                    
00000800  __SYSMEM_SIZE                       
0000085c  fmcBus2Check                        
00000898  fmcECCcheck                         
00000938  checkB0RAMECC                       
00000b28  checkB1RAMECC                       
00000d18  checkFlashECC                       
00000df4  custom_dabort                       
00000df8  stcSelfCheckFail                    
00000dfc  cpuSelfTestFail                     
00000e00  vimParityCheck                      
00000eac  dmaParityCheck                      
00000f58  het1ParityCheck                     
00001004  htu1ParityCheck                     
000010c0  het2ParityCheck                     
000011bc  htu2ParityCheck                     
0000126c  adc1ParityCheck                     
00001358  adc2ParityCheck                     
00001410  can1ParityCheck                     
000014e8  can2ParityCheck                     
000015b0  can3ParityCheck                     
00001684  mibspi1ParityCheck                  
0000178c  mibspi3ParityCheck                  
000018b0  mibspi5ParityCheck                  
000019d0  checkRAMECC                         
00001c18  checkClockMonitor                   
00001cf4  checkFlashEEPROMECC                 
00001e8c  checkPLL1Slip                       
00001f8c  checkPLL2Slip                       
00002050  checkRAMAddrParity                  
0000215c  checkRAMUERRTest                    
000022c4  fmcBus1ParityCheck                  
000023ec  pbistFail                           
00002470  pbistGetConfigValue                 
000025bc  stcGetConfigValue                   
00002660  efcGetConfigValue                   
000026fc  ccmr4GetConfigValue                 
0000276c  errata_PBIST_4                      
00002ad0  enableParity                        
00002b58  disableParity                       
00002cfc  setupPLL                            
00002d5c  trimLPO                             
00002da4  setupFlash                          
00002de4  periphInit                          
00002e38  mapClocks                           
00002f60  systemInit                          
0000302c  systemPowerDown                     
0000305c  systemGetConfigValue                
00003470  tcmflashGetConfigValue              
0000361c  sramGetConfigValue                  
00003830  Fray_testInit                       
0000388c  Fray_Init                           
000039a4  Fray_PrepareHeader                  
00003ac0  Fray_INT_Disable                    
00003b0c  Fray_INT_Enable                     
00003b2c  Fray_CC_CONFIG                      
00003b78  Fray_CC_READY                       
00003c5c  Fray_Transmit                       
00003d0c  Fray_Receive                        
00003d94  Fray_AllowColdStart                 
00003de4  Fray_StartCommunication             
00003e24  header_crc_calc                     
00003f94  Fray_TestMode                       
00004074  Fray_NormalMode                     
00004160  FTU_Test                            
000048a0  ftuErrorNotification                
00004960  ftuStatusChangeNotification         
000049ac  frayErrorNotification               
000049c4  frayStatusChangeNotification        
00004a90  vimInit                             
00004b64  vimChannelMap                       
00004c00  vimEnableInterrupt                  
00004d58  vimDisableInterrupt                 
00004ddc  vimGetConfigValue                   
00005328  sciInit                             
00005414  sciSetFunctional                    
00005434  sciSetBaudrate                      
000054b8  sciIsTxReady                        
000054d4  sciSendByte                         
00005504  sciSend                             
00005638  sciIsRxReady                        
00005654  sciIsIdleDetected                   
00005670  sciRxError                          
000056a0  sciReceiveByte                      
000056cc  sciReceive                          
000057a4  sciEnableLoopback                   
000057d8  sciDisableLoopback                  
000057fc  sciEnableNotification               
00005864  sciDisableNotification              
000058cc  sciGetConfigValue                   
00005a78  muxInit                             
00005dfc  pinmuxGetConfigValue                
000060a8  esmInit                             
000061d4  esmError                            
000061f0  esmEnableError                      
0000621c  esmDisableError                     
00006248  esmTriggerErrorPinReset             
00006258  esmActivateNormalOperation          
00006268  esmEnableInterrupt                  
00006294  esmDisableInterrupt                 
000062c0  esmSetInterruptLevel                
0000633c  esmClearStatus                      
0000637c  esmClearStatusBuffer                
000063a8  esmSetCounterPreloadValue           
000063c8  esmGetStatus                        
0000643c  esmGetStatusBuffer                  
0000649c  esmEnterSelfTest                    
00006524  esmSelfTestStatus                   
00006574  esmGetConfigValue                   
000066d4  gioInit                             
000067a4  gioSetDirection                     
000067c4  gioSetBit                           
00006814  gioSetPort                          
00006834  gioGetBit                           
0000685c  gioGetPort                          
00006874  gioToggleBit                        
000068cc  gioEnableNotification               
00006934  gioDisableNotification              
0000699c  gioGetConfigValue                   
00006b58  FTU_Init                            
00006c24  FTU_TCR_Init                        
00006cb0  FTU_Enable                          
00006cd0  FTU_Disable                         
00006cf0  FTU_TOSM_IntEnable                  
00006d40  FTU_TOCC_IntEnable                  
00006d90  FTU_TOSM_Trigger                    
00006de0  FTU_TOCC_Trigger                    
00006e30  FTU_Event_ETESMS                    
00006e80  FTU_Transfer_INT_Enable             
00006ea0  FTU_Error_INT_Enable                
00006ec0  FTU_ETESM_Clear                     
00006ee8  FTU_TTCC_Clear                      
00006f10  FTU_TTSM_Clear                      
00006f40  _coreInitRegisters_                 
0000704c  _coreInitStackPointer_              
00007098  _getCPSRValue_                      
000070a0  _gotoCPUIdle_                       
000070b8  _coreEnableVfp_                     
000070d0  _coreEnableEventBusExport_          
000070e8  _coreDisableEventBusExport_         
00007100  _coreEnableRamEcc_                  
00007118  _coreDisableRamEcc_                 
00007130  _coreEnableFlashEcc_                
0000714c  _coreDisableFlashEcc_               
00007164  _coreEnableIrqVicOffset_            
0000717c  _coreGetDataFault_                  
00007184  _coreClearDataFault_                
00007198  _coreGetInstructionFault_           
000071a0  _coreClearInstructionFault_         
000071b4  _coreGetDataFaultAddress_           
000071bc  _coreClearDataFaultAddress_         
000071d0  _coreGetInstructionFaultAddress_    
000071d8  _coreClearInstructionFaultAddress_  
000071ec  _coreGetAuxiliaryDataFault_         
000071f4  _coreClearAuxiliaryDataFault_       
00007208  _coreGetAuxiliaryInstructionFault_  
00007210  _coreClearAuxiliaryInstructionFault_
00007224  _disable_interrupt_                 
0000722c  _disable_FIQ_interrupt_             
00007234  _disable_IRQ_interrupt_             
0000723c  _enable_interrupt_                  
00007244  _esmCcmErrorsClear_                 
000072cc  _errata_CORTEXR4_57_                
000072e4  _errata_CORTEXR4_66_                
000072fc  __TI_PINIT_Base                     
00007300  __TI_PINIT_Limit                    
00007304  append                              
00007344  Int2Str                             
00007438  PrintString                         
00007484  PrintInteger                        
000075ec  PrintChar                           
00007614  GetChar                             
00007640  _c_int00                            
000078d8  vimParityErrorHandler               
00007b6c  esmHighInterrupt                    
00007cc4  frayHighLevelInterrupt              
00007d6c  frayLowLevelInterrupt               
00007e0c  _pmuInit_                           
00007e60  _pmuEnableCountersGlobal_           
00007e78  _pmuDisableCountersGlobal_          
00007e90  _pmuResetCycleCounter_              
00007ea8  _pmuResetEventCounters_             
00007ec0  _pmuResetCounters_                  
00007ed8  _pmuStartCounters_                  
00007ee0  _pmuStopCounters_                   
00007ee8  _pmuSetCountEvent_                  
00007ef4  _pmuGetCycleCount_                  
00007efc  _pmuGetEventCount_                  
00007f0c  _pmuGetOverflow_                    
00007f20  _dabort                             
00008008  esmGroup1Notification               
00008018  esmGroup2Notification               
00008028  memoryPort0TestFailNotification     
00008044  memoryPort1TestFailNotification     
00008060  gioNotification                     
00008074  sciNotification                     
00008088  pwmNotification                     
000080a0  edgeNotification                    
000080b4  hetNotification                     
000080c8  dmaGroupANotification               
000080dc  ftuTransferInterrupt                
00008148  ftuErrorInterrupt                   
00008199  __aeabi_memcpy                      
00008199  __aeabi_memcpy4                     
00008199  __aeabi_memcpy8                     
00008199  memcpy                              
00008235  __aeabi_memclr                      
00008235  __aeabi_memclr4                     
00008235  __aeabi_memclr8                     
00008237  __aeabi_memset                      
00008237  __aeabi_memset4                     
00008237  __aeabi_memset8                     
0000823d  memset                              
000082b0  gio_LED_Blinky                      
00008321  __TI_auto_init                      
000083f8  __aeabi_idiv                        
000083f8  __aeabi_idivmod                     
00008450  __aeabi_uidiv                       
00008450  __aeabi_uidivmod                    
000084a4  main                                
000084f9  copy_in                             
00008545  C$$EXIT                             
00008549  abort                               
00008551  exit                                
00008585  strlen                              
0000859c  phantomInterrupt                    
000085b1  _register_unlock                    
000085b7  _register_lock                      
000085bf  _nop                                
000085c5  __TI_decompress_none                
000085d3  __TI_zero_init                      
000085df  __TI_decompress_rle24               
000087e0  __TI_Handler_Table_Base             
000087ec  __TI_Handler_Table_Limit            
000087f8  __TI_CINIT_Base                     
00008808  __TI_CINIT_Limit                    
08001500  _sys_memory                         
08001d00  FRAY_SysMemory                      
08001f28  g_ftuDataRCVED                      
08001f2c  g_ftuErrorFlag                      
08001f30  FRAY_TX_Payload                     
08001fc0  _lock                               
08001fc4  _unlock                             
08001fc8  __TI_cleanup_ptr                    
08001fcc  __TI_dtors_ptr                      
ffffffff  __binit__                           
ffffffff  __c_args__                          
ffffffff  binit                               
UNDEFED   SHT$$INIT_ARRAY$$Base               
UNDEFED   SHT$$INIT_ARRAY$$Limit              

[266 symbols]
