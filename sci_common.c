//*****************************************************************************
//
// sci_common.c: Common used SCI functions
// Author      : QJ <PERSON>. <EMAIL>
// Date        : 9-19-2012
//
// Copyright (c) 2006-2011 Texas Instruments Incorporated.  All rights reserved.
// Software License Agreement
//
// Texas Instruments (TI) is supplying this software for use solely and
// exclusively on TI's microcontroller products. The software is owned by
// TI and/or its suppliers, and is protected under applicable copyright
// laws. You may not combine this software with "viral" open-source
// software in order to form a larger program.
//
// THIS SOFTWARE IS PROVIDED "AS IS" AND WITH ALL FAULTS.
// NO WARRANTIES, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING, BUT
// NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE APPLY TO THIS SOFTWARE. TI SHALL NOT, UNDER ANY
// CIRCUMSTANCES, BE LIABLE FOR SPECIAL, INCIDENTAL, OR CONSEQUENTIAL
// DAMAGES, FOR ANY REASON WHATSOEVER.
//
//*****************************************************************************


#include "sci_common.h"
#include "sci.h"
#include <string.h>

void append(char* s, char c)
{
        int len = strlen(s);
        s[len] = c;
        s[len+1] = '\0';
}
/**
  * @brief  Convert an Integer to a string
  * @param  str: The string
  * @param  intnum: The intger to be converted
  * @retval None
  */
void Int2Str(char* str, int intnum)
{
  unsigned int i, Div = 1000000, j = 0, Status = 0;

  for (i = 0; i < 10; i++)
  {
    str[j++] = (intnum / Div) + 48;

    intnum = intnum % Div;
    Div /= 10;
    if ((str[j-1] == '0') & (Status == 0))
    {
      j = 0;
    }
    else
    {
      Status++;
    }
  }
}


void PrintString(char *s)
{
	while(*s != '\0')
	{
		while(!sciIsTxReady(sciREG));
		sciSendByte(sciREG, (uint8)*s);
		s++;
	}
//	sciSend(sciREG,sizeof(s),(uint8 *)s);
}

void PrintInteger(int d)
{
	int t_len,t_index;
	char t_str[20],t_ch;

	if(d == 0)
	{
		while(!sciIsTxReady(sciREG));
		sciSendByte(sciREG, (uint8)'0');
	}
	else
	{
		t_len = 0;
		while(d != 0)
		{
			t_str[t_len++] = '0' + d % 10;
			d = d / 10;
		}
		// roll back
		for(t_index = 0; t_index < (t_len / 2); t_index ++)
		{
			t_ch = t_str[t_index];
			t_str[t_index] = t_str[t_len - t_index - 1];
			t_str[t_len - t_index - 1] = t_ch;
		}
		for(t_index = 0; t_index < t_len; t_index ++)
		{
			while(!sciIsTxReady(sciREG));
			sciSendByte(sciREG, (uint8)t_str[t_index]);
		}
	}
}

void PrintChar(char s)
{
	while(!sciIsTxReady(sciREG));
	sciSendByte(sciREG, (uint8)s);
}


char GetChar(void)
{
	while(!sciIsRxReady(sciREG));
	return (char)sciReceiveByte(sciREG);
}

